package upetmart_po

import (
	"time"
	"xorm.io/xorm"
)

// User 用户扩展表
type User struct {
	Id               int       `xorm:"not null pk autoincr comment('ID') INT(11)"`
	MemberId         int       `xorm:"index(idx_mid_oid_unique) comment('电商的用户ID') INT(11)"`
	ScrmUserId       string    `xorm:"not null index comment('32位的用户userid') VARCHAR(32)"`
	OrgId            int16     `xorm:"index(idx_mid_oid_unique) default 0 comment('所属主体id:1-阿闻电商 2-极宠家 3-宠商云 4-百林康源') SMALLINT"`
	Source           int       `xorm:"default 0 comment('来源：0-默认 1-贵族裂变活动') TINYINT"`
	MemberTruename   string    `xorm:"default '' comment('真实姓名') VARCHAR(20)"`
	WeixinUnionid    string    `xorm:"default '' comment('微信用户统一标识') VARCHAR(50)"`
	WeixinMiniOpenid string    `xorm:"default '' comment('小程序openid') VARCHAR(50)"`
	CreateTime       time.Time `xorm:"datetime created 'create_time'" json:"create_time"` // 创建时间
	UpdateTime       time.Time `xorm:"datetime updated 'update_time'" json:"update_time"` // 更新时间
}

type UserMember struct {
	User         `xorm:"extends"`
	MemberMobile string `xorm:"member_mobile"`
}

// TableName 返回表名
func (u *User) TableName() string {
	return "eshop.users"
}

// Get 根据ID获取用户信息
func (u *User) Get(session *xorm.Session, id int) (bool, error) {
	return session.ID(id).Get(u)
}

// GetByMemberIdAndOrgId 根据会员ID和组织ID获取用户信息
func (u *User) GetByMemberIdAndOrgId(session *xorm.Session, memberId int, orgId int) (bool, error) {
	return session.Where("member_id = ? AND org_id = ?", memberId, orgId).Get(u)
}

// GetByOpenId 根据openid获取用户信息,连接upet_member表
func (u *User) GetByOpenId(session *xorm.Session, openId string, orgId int) (bool, error) {
	return session.Table("users").Alias("u").
		Join("LEFT", "upetmart.upet_member um", "u.member_id = um.member_id").
		Where("u.weixin_mini_openid = ? and u.org_id = ?", openId, orgId).
		Get(u)
}

// GetByScrmUserId 根据ScrmUserId获取用户信息
func (u *User) GetByScrmUserId(session *xorm.Session, scrmUserId string, orgId int) (bool, error) {
	return session.Where("scrm_user_id = ? and org_id=?", scrmUserId, orgId).Get(u)
}

// UserMemberView 用户会员视图结构体
type UserMemberView struct {
	// 用户ID
	Id int `json:"id"`
	// 用户ID
	MemberId int `json:"member_id"`
	// 用户ID
	ScrmUserId string `json:"scrm_user_id"`
	// 组织ID
	OrgId int16 `json:"org_id"`
	// 微信用户统一标识
	WeixinUnionid string `json:"weixin_unionid"`
	// 微信小程序openid
	WeixinMiniOpenid string `json:"weixin_mini_openid"`
	// 用户名称
	MemberName string `json:"member_name"`
	// 用户性别
	MemberSex int `json:"member_sex"`
	// 用户手机号
	MemberMobile string `json:"member_mobile"`
	// 用户注册时间
	MemberTime string `json:"member_time"`
	// 用户最后登录时间
	MemberLoginTime string `json:"member_login_time"`
	// 用户最后购买时间
	LastPurchaseTime string `json:"last_purchase_time" xorm:"-"`
	// 用户订单数量
	OrderCount int `json:"order_count" xorm:"-"`
}

// GetUsersByOrgId 获取指定组织的用户列表
func GetUsersByOrgId(engine *xorm.Engine, orgId int16, pageIndex, pageSize int) ([]UserMemberView, int64, error) {
	var (
		results []UserMemberView
		total   int64
		err     error
	)

	// 构建查询
	query := engine.Table("users").
		Join("LEFT", "upetmart.upet_member", "users.member_id = upet_member.member_id").
		Where("users.org_id = ?", orgId)

	// 查询总数
	total, err = query.Count(&UserMemberView{})
	if err != nil {
		return nil, 0, err
	}

	// 查询数据，添加分页
	err = query.
		Select(`
			users.id, 
			users.member_id, 
			users.scrm_user_id, 
			users.org_id, 
			users.weixin_unionid, 
			users.weixin_mini_openid,
			upet_member.member_name, 
			upet_member.member_sex, 
			upet_member.member_mobile, 
			upet_member.member_avatar,
			upet_member.member_time as register_time,
			upet_member.member_login_time as last_login_time,
			'' as last_purchase_time,
			0 as order_count
		`).
		Limit(pageSize, (pageIndex-1)*pageSize).
		OrderBy("users.id DESC").
		Find(&results)

	if err != nil {
		return nil, 0, err
	}

	return results, total, nil
}

// 附加查询条件的用户查询
func GetUsersByCondition(engine *xorm.Engine, condition map[string]interface{}, pageIndex, pageSize int) ([]UserMemberView, int64, error) {
	var (
		results []UserMemberView
		total   int64
		err     error
	)

	// 构建基础查询
	query := engine.Table("users").Alias("u").
		Join("LEFT", "upetmart.upet_member um", "u.member_id = um.member_id")

	// 添加条件
	if orgId, ok := condition["org_id"].(int16); ok {
		query = query.Where("u.org_id = ?", orgId)
	}

	if mobile, ok := condition["mobile"].(string); ok && mobile != "" {
		query = query.Where("um.member_mobile LIKE ?", "%"+mobile+"%")
	}

	if nickname, ok := condition["nickname"].(string); ok && nickname != "" {
		query = query.Where("um.member_name LIKE ?", "%"+nickname+"%")
	}

	// 查询总数
	total, err = query.Count(&UserMemberView{})
	if err != nil {
		return nil, 0, err
	}

	// 查询数据，添加分页
	err = query.
		Select(`
			users.id, 
			users.member_id, 
			users.scrm_user_id, 
			users.org_id, 
			users.weixin_unionid, 
			users.weixin_mini_openid,
			upet_member.member_name, 
			upet_member.member_sex, 
			upet_member.member_mobile, 
			upet_member.member_avatar,
			upet_member.member_time as register_time,
			upet_member.member_login_time as last_login_time,
			'' as last_purchase_time,
			0 as order_count
		`).
		Limit(pageSize, (pageIndex-1)*pageSize).
		OrderBy("users.id DESC").
		Find(&results)

	if err != nil {
		return nil, 0, err
	}

	return results, total, nil
}

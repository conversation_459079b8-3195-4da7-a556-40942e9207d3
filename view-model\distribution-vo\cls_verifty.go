package distribution_vo

// ClsVerifyPageReq 防伪码查询请求
type ClsVerifyPageReq struct {
	// 页码
	PageIndex int `json:"page_index"`
	// 每页条数
	PageSize int `json:"page_size"`
	// 公司名称
	CompanyName string `json:"company_name" validate:"required"`
	// 防伪码
	Code string `json:"code"`
	// 数据类型：1-统计列表 2-明细列表
	DataType int `json:"data_type" validate:"required,oneof=1 2"`
}

// ClsVerifyPageData 防伪码查询数据
type ClsVerifyPageData struct {
	// 主键
	Id int64 `json:"id"`
	// 公司名称
	CompanyName string `json:"company_name"`
	// 防伪码
	Code string `json:"code"`
	// 验证次数(仅统计视图)
	VerifyCount int `json:"verify_count"`
	// 查询时间
	VerifyTime string `json:"verify_time"`
	// 用户Openid
	UserOpenid string `json:"user_openid"`
	// 用户会员id
	UserId string `json:"user_id"`
	// 用户手机号(带星)
	UserPhone string `json:"user_phone"`
	// 查询方式 1: 手动输入 1: 扫码查询
	VerifyType int `json:"verify_type"`
	// 查询次数描述
	VerifyDesc string `json:"verify_desc"`
	// 查询所在地
	VerifyLocation string `json:"verify_location"`
	// 查询入口
	VerifyEntry string `json:"verify_entry"`
	//手机号加密
	EncryptUserPhone string `json:"encrypt_user_phone"`
}

// ClsVerifyPageResp 防伪码查询响应
type ClsVerifyPageResp struct {
	Code    int                 `json:"code"`
	Message string              `json:"message"`
	Data    []ClsVerifyPageData `json:"data"`
	Total   int                 `json:"total"`
}

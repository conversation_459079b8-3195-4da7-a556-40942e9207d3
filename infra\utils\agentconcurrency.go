package utils

import (
	"eShop/infra/cache"
	"eShop/infra/config"
	"eShop/infra/log"
	cache_source "eShop/services/distribution-service/enum/cache-source"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"fmt"
)

func AcquireLock(rdb cache.MemoryCache, key string, ttl time.Duration) (string, bool) {
	logPrefix := fmt.Sprintf("贵族裂变活动-获取分布式锁%s====", key)
	// 生成唯一锁标识
	lockValue := fmt.Sprintf("%d:%d", time.Now().UnixNano(), rand.Int63())

	// 原子操作：SET key value NX EX seconds
	result, err := rdb.SetNX(key, lockValue, ttl)
	log.Infof("%s result:%v|err:%v", logPrefix, result, err)
	if err != nil || !result {
		return "", false
	}
	return lockValue, true
}

func ReleaseLock(rdb cache.MemoryCache, key, lockValue string) bool {
	logPrefix := fmt.Sprintf("贵族裂变活动-释放分布式锁%s====", key)
	// 使用 Lua 脚本保证原子性
	script := `
	if redis.call("GET", KEYS[1]) == ARGV[1] then
		return redis.call("DEL", KEYS[1])
	else
		return 0
	end`

	result, err := rdb.Eval(script, []string{key}, lockValue)
	log.Infof("%s result:%v|err:%v", logPrefix, result, err)
	return err == nil && result == 1
}

var (
	AtomicPollScriptSha    string
	CozeAgentCount         int    = 1
	CozeAgentListKey       string = "coze_agent:coze_agents_list"
	CozePollIndexKey       string = "coze_agent:coze_poll_index"
	TokenPrefix            string = "coze_agent:coze_agent_tokens:"
	GuizuPetImageCozeBotId string = "guizu_pet_image_coze_bot_id"
	//MaxConcurrent          int    = 7
	initLockKey   string = "coze_agent:init_agent_system_lock"  // 分布式锁 Key
	initStatusKey string = "coze_agent:coze_agents_initialized" // 初始化状态 Key
)

// 初始化智能体系统（幂等） （注意：如果修改了智能体数量或者并发数，需要删除redis中的coze_agents_initialized状态）
func InitAgentSystem(rdb cache.MemoryCache) {
	logPrefix := "贵族裂变活动-初始化coze智能体列表以及每个智能体并发数量redis桶锁===="
	log.Info(logPrefix, "开始初始化")
	// 检查是否已初始化
	exist, err := rdb.Exists(initStatusKey)
	if err != nil {
		log.Errorf("%s Failed to check init status: %v", logPrefix, err)
		return
	}
	if exist == 1 {
		return
	}

	lockValue, locked := AcquireLock(rdb, initLockKey, 10*time.Minute)
	if !locked {
		log.Errorf("%s Failed to acquire init lock, waiting...", logPrefix)
		time.Sleep(1 * time.Second)
		exist, err = rdb.Exists(initStatusKey)
		if err != nil {
			log.Errorf("%s Failed to check init status: %v", logPrefix, err)
			return
		}
		if exist == 1 {
			return // 其他服务器已完成初始化
		}
		log.Errorf("%s Agent system initialization failed after retry", logPrefix)
		return
	}

	defer func() {
		ReleaseLock(rdb, initLockKey, lockValue)
	}()

	// 二次检查（防止锁期间其他服务已完成初始化）
	exist, err = rdb.Exists(initStatusKey)
	if err != nil {
		log.Errorf("%s Failed to check init status: %v", logPrefix, err)
		return
	}
	if exist == 1 {
		return
	}

	// === 执行实际初始化 ===
	botIds := config.Get(GuizuPetImageCozeBotId)
	botIdSli := strings.Split(botIds, ",")
	agentIDs := make([]interface{}, 0)

	for _, botId := range botIdSli {
		if botId != "" {
			agentIDs = append(agentIDs, botId)
		}
	}
	CozeAgentCount = len(agentIDs)
	log.Infof("%s CozeAgentCount:%d", logPrefix, CozeAgentCount)
	fmt.Printf("贵族裂变活动-初始化coze智能体列表以及每个智能体并发数量redis桶锁 CozeAgentCount:%d\n", CozeAgentCount)
	// 使用事务确保原子操作
	pipe := rdb.Pipeline()
	pipe.Del(CozeAgentListKey)
	pipe.RPush(CozeAgentListKey, agentIDs...)
	pipe.Set(CozePollIndexKey, 0, 0)
	pipe.Set(initStatusKey, "1", 24*time.Hour) // 设置初始化状态
	_, err = pipe.Exec()
	if err != nil {
		log.Errorf("%s Failed to execute pipeline: %v", logPrefix, err)
		return
	}

	LoadAtomicPollScript(rdb)
	fmt.Printf("%s LoadAtomicPollScript为%s\n", logPrefix, AtomicPollScriptSha)

	InitAllTokenBuckets(rdb)

}

// 加载原子轮询脚本
func LoadAtomicPollScript(rdb cache.MemoryCache) {

	script := `
		-- KEYS[1]: 轮询索引的键 (coze_agent:coze_poll_index)
		-- KEYS[2]: 智能体列表的键 (coze_agent:coze_agents_list)

		-- 获取当前索引
		local idx = tonumber(redis.call("GET", KEYS[1]))
		if idx == nil then
			idx = 0
		end

		-- 获取智能体列表
		local agents = redis.call("LRANGE", KEYS[2], 0, -1)
		local agentCount = #agents

		if agentCount == 0 then
			return nil
		end

		-- 计算下一个索引
		local nextIdx = (idx + 1) % agentCount
		if nextIdx < 0 then
   			 nextIdx = 0
		end
		-- 更新索引
		redis.call("SET", KEYS[1], nextIdx)

		-- 返回当前智能体ID (Lua索引从1开始)
		return agents[idx + 1]
	`

	// 加载脚本并保存SHA
	sha, err := rdb.ScriptLoad(script)
	if err != nil {
		log.Errorf("LoadAtomicPollScript Failed to load Lua script: %v", err)
	}
	AtomicPollScriptSha = sha
	fmt.Printf("LoadAtomicPollScript Loaded atomic poll script: %s\n", sha)
	log.Infof("LoadAtomicPollScript Loaded atomic poll script: %s", sha)
}

// 初始化所有令牌桶
func InitAllTokenBuckets(rdb cache.MemoryCache) {
	// 获取所有智能体
	agentIDs, err := rdb.LRange(CozeAgentListKey, 0, -1)
	if err != nil {
		log.Fatalf(" InitAllTokenBuckets Failed to get agent list: %v", err)
	}

	for _, agentID := range agentIDs {
		InitTokenBucket(rdb, agentID)
	}
}

// 初始化单个令牌桶
func InitTokenBucket(rdb cache.MemoryCache, agentID string) {
	key := TokenPrefix + agentID
	pipe := rdb.Pipeline()
	pipe.Del(key)

	tokens := make([]interface{}, 0)
	maxConcurrentNum := GetMaxConcurrentNum()
	for i := 0; i < maxConcurrentNum; i++ {
		tokens = append(tokens, "1")
	}
	pipe.LPush(key, tokens...)
	_, err := pipe.Exec()
	if err != nil {
		log.Errorf("InitTokenBucket Failed to execute pipeline: %v", err)
	}

}

// 获取令牌 → 调用 Coze → 释放令牌
func CallCozeWithToken(rdb cache.MemoryCache, requestID int, agentID string) error {
	key := TokenPrefix + agentID

	// 1. 获取令牌（阻塞等待）
	_, err := rdb.BRPop(2*time.Second, key)
	if err != nil {
		fmt.Printf("bbbbbbb Request %d got token faild for agent %s\n", requestID, agentID)
		return err
	}
	//log.Errorf("Request %d got token for agent %s: %v", requestID, agentID, token)
	fmt.Printf("aaaaaaa Request %d got token for agent %s\n", requestID, agentID)
	// 2. 确保释放令牌
	defer func() {
		rdb.LPush(key, "1")

	}()

	// 3. 调用Coze接口
	time.Sleep(1 * time.Second)
	return nil
}

// 定时检查并补充令牌桶
func TokenRefiller(rdb cache.MemoryCache) {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		// 获取所有智能体
		agentIDs, err := rdb.LRange(CozeAgentListKey, 0, -1)
		if err != nil {
			log.Errorf("TokenRefiller Token refiller failed to get agents: %v", err)
			continue
		}

		// 使用管道批量操作
		pipe := rdb.Pipeline()
		maxConcurrentNum := GetMaxConcurrentNum()
		for _, agentID := range agentIDs {
			key := TokenPrefix + agentID
			currentTokens, _ := rdb.LLen(key)
			if currentTokens < int64(maxConcurrentNum) {
				refillCount := int64(maxConcurrentNum) - currentTokens
				for i := 0; i < int(refillCount); i++ {
					pipe.LPush(key, "1")
				}
				log.Errorf("TokenRefiller Refilling %d tokens for agent %s", refillCount, agentID)
			}
		}

		// 执行所有补充操作
		if _, err := pipe.Exec(); err != nil {
			log.Errorf("TokenRefiller Token refill failed: %v", err)
		}
	}
}

// 原子操作获取下一个智能体
func GetNextAgentAtomic(rdb cache.MemoryCache) (string, error) {
	keys := []string{CozePollIndexKey, CozeAgentListKey}

	// 使用EVALSHA执行脚本
	result, err := rdb.EvalSha(AtomicPollScriptSha, keys)
	if err != nil {
		// 如果脚本未加载，尝试重新加载
		if err.Error() == "NOSCRIPT No matching script. Please use EVAL." {
			LoadAtomicPollScript(rdb)
			result, err = rdb.EvalSha(AtomicPollScriptSha, keys)
		}

		if err != nil {
			return "", fmt.Errorf("failed to execute atomic poll script: %v", err)
		}
	}

	if result == nil {
		return "", fmt.Errorf("no agents available")
	}

	return result.(string), nil
}

// 贵族裂变活动 - 每个用户最多生成作品数
func GetMaxImageNum() (out int) {
	defaultNum := 3
	redisConn := cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	// 查找对应的邀请码记录
	key := "coze_agent:guizu_user_max_image_num"
	v := redisConn.Get(string(cache_source.EShop), key)
	if len(v) == 0 || v[0].(string) == "" {
		redisConn.Save(string(cache_source.EShop), key, defaultNum, 0)
		return defaultNum
	}
	vInt, err := strconv.Atoi(v[0].(string))
	if err != nil {
		log.Errorf("GetMaxImageNum Failed to convert to int: %v", err)
		return defaultNum
	}
	return vInt

}

func GetMaxConcurrentNum() (out int) {
	defaultNum := 7
	redisConn := cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	// 查找对应的邀请码记录
	key := "coze_agent:guizu_max_concurrent_num"
	v := redisConn.Get(string(cache_source.EShop), key)
	if len(v) == 0 || v[0].(string) == "" {
		redisConn.Save(string(cache_source.EShop), key, defaultNum, 0)
		return defaultNum
	}
	vInt, err := strconv.Atoi(v[0].(string))
	if err != nil {
		log.Errorf("GetMaxConcurrentNum Failed to convert to int: %v", err)
		return defaultNum
	}
	return vInt

}

package async

import (
	marketing_po "eShop/domain/marketing-po"
	"eShop/infra/cache"
	"eShop/infra/log"
	"eShop/infra/utils"
	"eShop/services/common"
	cache_source "eShop/services/distribution-service/enum/cache-source"
	"eShop/services/marketing-service/services"
	viewmode "eShop/view-model"
	marketing_vo "eShop/view-model/marketing-vo"
	"encoding/json"
	"errors"
	"fmt"

	"time"
)

type PetArtworkService struct {
	common.BaseService
}

// parJson  异步任务的参数JSON数据 【样例，不要删掉这个】
// func (h ProductAddService) OperationFunc(parJson string, org_id int) (*viewmode.ImportResult, error) {
// 	out := new(viewmode.ImportResult)

// 	//200 表示处理正常，会确认MQ，
// 	out.Code = 200
// 	//错误数量 有需要就写，比如批量操作100个商品，有成功有失败，就需要设置这个
// 	out.FailNum = 0
// 	out.SuccessNum = 5
// 	//如果有需要上传的文件，自己处理上传后，把URL写入到下面这个参数
// 	out.QiniuUrl = ""
// 	//错误的时候返回错误信息，成功的时候可以返回成功的一些内容，比如 （美团 成功100个商品 失败5个商品，饿了么成功80个 失败100个）  ，内容不超过1000个字符串
// 	out.Message = ""

//		return out, nil
//	}

func (h PetArtworkService) OperationFunc(parJson string, org_id int) (out *viewmode.ImportResult, err error) {
	logPrefix := "贵族裂变活动 - AI生成贵族宠物图任务(极宠家)OperationFunc===="
	log.Info(logPrefix, "入参：", parJson)
	utils.RunningDuration(logPrefix)()
	h.Begin()
	defer h.Close()
	session := h.Engine.NewSession()
	defer session.Close()
	out = new(viewmode.ImportResult)
	//200 表示处理正常，会确认MQ，
	out.Code = 200
	in := marketing_po.PetArtwork{}
	if err = json.Unmarshal([]byte(parJson), &in); err != nil {
		log.Error(logPrefix, "解析参数失败", err)
		out.Message = "解析参数失败"
		return
	}
	logPrefix = fmt.Sprintf("%s-作品id:%d", logPrefix, in.Id)
	log.Infof("%s-开始调用coze接口生成宠物图====", logPrefix)

	// 调用coze接口生成宠物图
	var rdb = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])

	// 重试获取智能体（最多5次）
	const maxRetries = 5
	var agentID string
	for i := 0; i < maxRetries; i++ {
		agentID, err = utils.GetNextAgentAtomic(rdb)
		if err == nil {
			break // 成功获取则跳出循环
		}
		log.Errorf("%s-获取智能体失败(尝试 %d/%d): %v", logPrefix, i+1, maxRetries, err)

		// 最后一次尝试不等待直接退出
		if i < maxRetries-1 {
			time.Sleep(1 * time.Second) // 添加短暂延迟避免立即重试
		} else {
			err = errors.New("需要再次放入队列处理|||获取智能体失败")
			return
		}
	}

	//  重试获取令牌（最多5次）
	key := utils.TokenPrefix + agentID
	const maxRetriesToken = 5
	for i := 0; i < maxRetriesToken; i++ {
		// 1. 获取令牌（阻塞等待）
		_, err = rdb.BRPop(1*time.Second, key)
		if err == nil {
			break // 成功获取则跳出循环
		}
		log.Errorf("%s-获取令牌失败(尝试 %d/%d): %v", logPrefix, i+1, maxRetriesToken, err)

		// 最后一次尝试不等待直接退出
		if i < maxRetriesToken-1 {
			time.Sleep(1 * time.Second) // 添加短暂延迟避免立即重试
		} else {
			err = errors.New("需要再次放入队列处理|||获取令牌失败")
			return
		}

	}

	log.Infof("%s-获取令牌成功", logPrefix)
	// 2. 确保释放令牌
	defer func() {
		a, b := rdb.LPush(key, "1")
		fmt.Println("LPush", a, b)
	}()

	// 3. 调用Coze接口
	petrtwork, err := services.NewPetArtworkService().Chat(marketing_vo.GuizuPetCozeChatReq{
		UserInfoId: in.ScrmUserId,
		BotType:    common.GuizuPetImageCozeBotId,
		PetName:    in.PetName,
		PetPhoto:   in.PetPhoto,
		PetGender:  in.PetGender,
		RefImg1:    in.RefImg1,
		RefImg2:    in.RefImg2,
		RefImg3:    in.RefImg3,
	})

	if err != nil {
		log.Errorf("%s-调用Coze接口失败: %v", logPrefix, err)
		out.Code = 400
		out.Message = err.Error()
		return
	}

	if petrtwork.NoblePetImg == "" {
		err = errors.New("调用Coze接口失败,返回空图片")
		out.Code = 400
		out.Message = "调用Coze接口失败,返回空图片"
		return
	}

	_, err = session.Table("eshop.pet_artwork").Where("id=?", in.Id).Cols("noble_pet_img").Update(map[string]interface{}{"deal_status": marketing_po.DealStatus_Success, "noble_pet_img": petrtwork.NoblePetImg})
	if err != nil {
		log.Errorf("%s-更新作品状态失败: %v", logPrefix, err)
		out.Code = 400
		out.Message = "更新作品状态失败" + err.Error()
		return
	}
	out.QiniuUrl = petrtwork.NoblePetImg
	out.SuccessNum = 1
	out.Message = ""
	return out, nil
}

// func (h AsyncService) InsertMqPetArtwork() {

// 	h.Begin()
// 	defer h.Close()

// 	session := h.Engine.NewSession()
// 	defer session.Close()
// 	var taskList []vo.TaskListAsync
// 	// 这里排除贵族裂变活动-AI生成贵族宠物图任务
// 	err := session.SQL("select * from eshop.task_list_async where is_push=0  and do_count<=5  and task_content=10  order by create_time asc limit 50").Find(&taskList)
// 	if err != nil {
// 		log.Error("获取异步任务报错" + err.Error())
// 		return
// 	}

// 	var rdb = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])

// 	// 初始化智能体系统
// 	utils.InitAgentSystem(rdb)

// 	// 加载Lua脚本
// 	utils.LoadAtomicPollScript(rdb)

// 	// 初始化所有令牌桶
// 	utils.InitAllTokenBuckets(rdb)

// 	var (
// 		wg          sync.WaitGroup
// 		results     = make(chan string, 100) // 按顺序收集结果
// 		resultMutex sync.Mutex
// 	)
// 	logPrefix := "贵族裂变活动-AI生成贵族宠物图任务-插入MQ"
// 	for i, x := range taskList {
// 		logPrefix = fmt.Sprintf("%s-任务ID:%d", logPrefix, x.Id)
// 		wg.Add(1)
// 		go func(requestID int) {
// 			defer wg.Done()

// 			// 使用原子操作获取下一个智能体
// 			var agentID string
// 			agentID, err = utils.GetNextAgentAtomic(rdb)

// 			if err != nil {
// 				log.Errorf("%s-获取智能体失败: %v", logPrefix, err)
// 				return
// 			}
// 			// 安全地收集结果
// 			resultMutex.Lock()
// 			results <- fmt.Sprintf("%s-请求 %d 分配到智能体: %s", logPrefix, requestID, agentID)
// 			resultMutex.Unlock()
// 			if err := h.CallCozeWithToken(rdb, requestID, agentID, x); err != nil {
// 				log.Errorf("%s-请求 %d 分配到智能体: %s 失败: %v", logPrefix, requestID, agentID, err)
// 				return
// 			}
// 		}(i)
// 		wg.Wait()
// 		close(results)
// 		fmt.Println("==== 智能体分配结果 ====")
// 		for result := range results {
// 			log.Infof("==== 智能体分配结果 ====%s\n", result)
// 			fmt.Println(result)
// 		}
// 	}
// }

// func (h AsyncService) CallCozeWithToken(rdb cache.MemoryCache, requestID int, agentID string, task vo.TaskListAsync) (err error) {
// 	logPrefix := fmt.Sprintf("贵族裂变活动-CallCozeWithToken:%d,agentID:%s,requestID:%d", task.Id, agentID, requestID)
// 	key := utils.TokenPrefix + agentID
// 	h.Begin()
// 	defer h.Close()

// 	session := h.Engine.NewSession()
// 	defer session.Close()
// 	// 1. 获取令牌（阻塞等待）
// 	_, err = rdb.BRPop(10*time.Second, key)
// 	if err != nil {
// 		return fmt.Errorf("%s-获取令牌失败: %v", logPrefix, err)
// 	}
// 	log.Infof("%s-获取令牌成功", logPrefix)

// 	// 2. 确保释放令牌
// 	defer func() {
// 		rdb.LPush(key, "1")

// 	}()

// 	// 3.放入rabbitmq
// 	bt, _ := json.Marshal(task)
// 	if ok := mq.PublishRabbitMQ(enum.AsyncQueue+cast.ToString(task.TaskContent), string(bt), "eshop"); !ok {
// 		log.Errorf("%s-mq推送失败,err:%v,bt:%s", logPrefix, err, string(bt))
// 		session.Exec("update eshop.task_list_async set task_status=4,err_mes=?,do_count=do_count+1 where id=", "mq推送失败："+err.Error(), task.Id)
// 		return fmt.Errorf("%s-mq推送失败,err:%s", logPrefix, err.Error())
// 	}
// 	//丢到MQ成功就改成进行中
// 	_, err = session.Exec("update eshop.task_list_async set is_push=1,err_mes='' where id= ?", task.Id)
// 	if err != nil {
// 		log.Errorf("%s-更新任务状态失败,err:%s", logPrefix, err.Error())
// 		return fmt.Errorf("%s-更新任务状态失败,err:%s", logPrefix, err.Error())
// 	}

// 	// 3. 调用Coze接口
// 	// fmt.Printf("bbbbbbb Request %d calling Coze API for agent %s...\n", requestID, agentID)
// 	// time.Sleep(5 * time.Second)
// 	// fmt.Printf("ccccccc Request %d to agent %s completed!\n", requestID, agentID)
// 	return nil
// }

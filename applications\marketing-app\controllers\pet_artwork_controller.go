package controllers

import (
	"eShop/infra/cache"
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/response"
	"eShop/infra/utils"
	cache_source "eShop/services/distribution-service/enum/cache-source"
	"eShop/services/marketing-service/services"
	"fmt"
	"net/http"
	"time"

	vo "eShop/view-model/marketing-vo"

	"github.com/go-chi/chi/v5"
)

// PetArtworkController 作品控制器
type PetArtworkController struct {
	service services.PetArtworkService
}

// NewPetArtworkController 创建作品控制器
func NewPetArtworkController(service services.PetArtworkService) *PetArtworkController {
	return &PetArtworkController{
		service: service,
	}
}

// RegisterRoutes 注册路由
func (c PetArtworkController) RegisterRoutes(r chi.Router) {
	r.Route("/marketing-app/awen/pet-artwork", func(r chi.Router) {
		r.Post("/create", c.Create)
		r.Post("/create-in-progress", c.CreateInProgress)
		r.Get("/exist-pk", c.ExistPk)
		r.Post("/select-pk", c.SelectedPk)
		r.Post("/page", c.Page)
		r.Get("/detail", c.Detail)
		r.Post("/test", c.ArtworkTest) // 测试贵族裂变活动-AI生成贵族宠物图任务
		r.Post("/chat", c.Chat)        // 贵族裂变活动-AI生成贵族宠物图任务
	})
}

// Create 创建宠物作品
// @Summary 创建宠物作品
// @Description 创建宠物作品
// @Tags 贵族裂变活动-宠物作品
// @Accept json
// @Produce json
// @Param artwork body vo.PetArtworkCreateReq true "创建作品请求"
// @Success 200 {object} marketing_po.PetArtwork "成功"
// @Failure 400 {object} marketing_po.PetArtwork "错误"
// @Router /marketing-app/awen/pet-artwork/create [post]
func (c PetArtworkController) Create(w http.ResponseWriter, r *http.Request) {
	logPrefix := "贵族裂变活动-创作作品===="
	cmd, err := utils.Bind[vo.PetArtworkCreateReq](r)
	if err != nil {
		log.Error(logPrefix, "解析参数错误: "+err.Error())
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	jwtInfo, err := jwtauth.GetJwtInfo(r)
	if err != nil {
		log.Error(logPrefix, "获取登录信息失败: err="+err.Error())
		response.BadRequest(w, "获取登录信息失败")
		return
	}
	if jwtInfo.Scrmid == "" {
		log.Error(logPrefix, "获取登录信息失败")
		response.BadRequest(w, "获取登录信息失败")
		return
	}

	cmd.ScrmUserId = jwtInfo.Scrmid
	cmd.Mobile = jwtInfo.Mobile

	petrtwork, err := c.service.Create(cmd)
	if err != nil {
		log.Error(logPrefix, "创建作品失败: err="+err.Error())
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithData(w, petrtwork)
}

// CreateInProgress 创建宠物作品-进行中
// @Summary 创建宠物作品-进行中
// @Description 创建宠物作品-进行中
// @Tags 贵族裂变活动-宠物作品
// @Accept json
// @Produce json
// @Param artwork body vo.CreateInProgressReq true "创建作品请求"
// @Success 200 {object} marketing_po.PetArtwork "成功"
// @Failure 400 {object} marketing_po.PetArtwork "错误"
// @Router /marketing-app/awen/pet-artwork/create-in-progress [post]
func (c PetArtworkController) CreateInProgress(w http.ResponseWriter, r *http.Request) {
	logPrefix := "贵族裂变活动-创作作品-进行中===="
	var cmd vo.CreateInProgressReq
	jwtInfo, err := jwtauth.GetJwtInfo(r)
	if err != nil {
		log.Error(logPrefix, "获取登录信息失败: err="+err.Error())
		response.BadRequest(w, "获取登录信息失败")
		return
	}
	if jwtInfo.Scrmid == "" {
		log.Error(logPrefix, "获取登录信息失败")
		response.BadRequest(w, "获取登录信息失败")
		return
	}

	cmd.ScrmUserId = jwtInfo.Scrmid
	redisConn := cache.NewMemberCache(cache.CacheSources[cache_source.EShop])

	// 加一个分布式锁， 避免用户连续重复点击
	lockKey := fmt.Sprintf("lock:pet_artwork_%s", cmd.ScrmUserId)
	result := redisConn.Get(string(cache_source.EShop), lockKey)
	msg := ""
	if len(result) > 0 && len(result[0].(string)) > 0 {
		msg = "您的作品后台正在创建中，请请稍后查看"
	}
	response.SuccessWithData(w, msg)
}

// ArtworkTest 测试贵族裂变活动-AI生成贵族宠物图任务
// @Summary 测试贵族裂变活动-AI生成贵族宠物图任务
// @Description
// @Tags 贵族裂变活动-宠物作品
// @Accept json
// @Produce json
// @Param command body vo.PetArtworkTestReq true "请求对话参数"
// @Success 200 {object} response.BaseResp "成功"
// @Failure 400 {object} response.BaseResp "请求错误"
// @Router /marketing-app/awen/pet-artwork/test [post]
func (c PetArtworkController) ArtworkTest(w http.ResponseWriter, r *http.Request) {

	cmd, err := utils.Bind[vo.PetArtworkTestReq](r)
	if err != nil {
		response.BadRequest(w, "无效的请求参数")
		return
	}

	err = c.service.ArtworkTest(cmd)
	if err != nil {
		log.Error("====贵族裂变活动-AI生成贵族宠物图任务====操作失败 err=", err.Error())
		response.BadRequest(w, err.Error())
		return
	}

	response.Success(w)
}

// Chat 测试贵族裂变活动-AI生成贵族宠物图任务
// @Summary 测试贵族裂变活动-AI生成贵族宠物图任务
// @Description
// @Tags 贵族裂变活动-宠物作品
// @Accept json
// @Produce json
// @Param command body vo.GuizuPetCozeChatReq true "请求对话参数"
// @Success 200 {object} response.BaseResp "成功"
// @Failure 400 {object} response.BaseResp "请求错误"
// @Router /marketing-app/awen/pet-artwork/chat [post]
func (c PetArtworkController) Chat(w http.ResponseWriter, r *http.Request) {

	cmd, err := utils.Bind[vo.GuizuPetCozeChatReq](r)
	if err != nil {
		response.BadRequest(w, "无效的请求参数")
		return
	}

	petrtwork, err := c.service.Chat(cmd)
	if err != nil {
		log.Error("====贵族裂变活动-AI生成贵族宠物图任务====操作失败 err=", err.Error())
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithData(w, petrtwork)
}

// ExistPk 用户是否已有参与PK的作品
// @Summary 用户是否已有参与PK的作品
// @Description 用户是否已有参与PK的作品
// @Tags 贵族裂变活动-宠物作品
// @Accept json
// @Produce json
// @Param artwork body vo.PetArtworkUpdateReq true "选中pk作品"
// @Success 200 {object} response.BaseResp "成功"
// @Failure 400 {object} response.BaseResp "错误"
// @Router /marketing-app/awen/pet-artwork/exist-pk [get]
func (c PetArtworkController) ExistPk(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.PetArtworkUpdateReq](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	has, err := c.service.ExistPk(nil, cmd.ScrmUserId)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithData(w, has)
}

// SelectedPk 选中pk作品
// @Summary 选中pk作品
// @Description 选中pk作品
// @Tags 贵族裂变活动-宠物作品
// @Accept json
// @Produce json
// @Param artwork body vo.PetArtworkUpdateReq true "选中pk作品"
// @Success 200 {object} response.BaseResp "成功"
// @Failure 400 {object} response.BaseResp "错误"
// @Router /marketing-app/awen/pet-artwork/select-pk [post]
func (c PetArtworkController) SelectedPk(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.PetArtworkUpdateReq](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	cmd.PkStatus = 1                          // 设置为参与PK状态
	cmd.PkTime = utils.GetTimeNow(time.Now()) // 更新参与PK时间
	err = c.service.SelectedPk(nil, cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.Success(w)
}

// Page 作品分页
// @Summary 作品分页
// @Description 获取宠物作品分页数据
// @Tags 贵族裂变活动-宠物作品
// @Accept json
// @Produce json
// @Param page body vo.PetArtworkPageReq true "分页请求"
// @Success 200 {object} response.Response[[]marketing_vo.PetArtworkResp] "成功获取作品分页数据"
// @Failure 400 {object} response.BaseResp "错误"
// @Router /marketing-app/awen/pet-artwork/page [post]
func (c PetArtworkController) Page(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.PetArtworkPageReq](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	page, total, err := c.service.Page(nil, cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithPage(w, page, int(total))
}

// Detail 作品详情
// @Summary 作品详情
// @Description 获取宠物作品详情数据
// @Tags 贵族裂变活动-宠物作品
// @Accept json
// @Produce json
// @Param detail body vo.PetArtworkDetailReq true "详情请求"
// @Success 200 {object} response.Response[vo.PetArtworkResp] "成功获取作品详情数据"
// @Failure 400 {object} response.BaseResp "错误"
// @Router /marketing-app/awen/pet-artwork/detail [post]
func (c PetArtworkController) Detail(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.PetArtworkDetailReq](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	if len(cmd.WorkCode) == 0 && cmd.Id <= 0 {
		response.BadRequest(w, "作品编号或ID不能为空")
		return
	}

	detail, err := c.service.Detail(nil, cmd)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithData(w, detail)
}

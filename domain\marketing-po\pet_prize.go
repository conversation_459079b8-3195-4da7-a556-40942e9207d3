// Package marketing_po 奖品领域模型
package marketing_po

import "time"

type PetPrize struct {
	Id               int       `xorm:"pk autoincr 'id'" json:"id"`
	UserId           string    `xorm:"user_id"`
	NickName         string    `xorm:"nick_name"`
	Mobile           string    `xorm:"mobile"`
	EnMobile         string    `xorm:"en_mobile"`
	PrizeType        int       `xorm:"prize_type"`
	WorkCode         string    `xorm:"work_code"`
	PrizeCount       int       `xorm:"prize_count"`
	ReceiveStatus    int       `xorm:"receive_status"`
	PrizeContent     string    `xorm:"prize_content"`
	CouponCode       string    `xorm:"coupon_code"`
	OrderSn          string    `xorm:"order_sn"`
	Address          string    `xorm:"address"`
	Receiver         string    `xorm:"receiver"`
	ReceiverMobile   string    `xorm:"receiver_mobile"`
	ReceiverEnMobile string    `xorm:"receiver_en_mobile"`
	ReceiveTime      time.Time `json:"receive_time" xorm:"default 'null' comment('领取时间') DATETIME 'receive_time'"`
	VerifyTime       time.Time `json:"verify_time" xorm:"default 'null' comment('核销时间') DATETIME 'verify_time'"`
	CreateTime       time.Time `xorm:"created" json:"create_time"`
	UpdateTime       time.Time `xorm:"updated" json:"update_time"`
}

func (PetPrize) TableName() string {
	return "pet_prize"
}

type PetPrizeExt struct {
	PetPrize     `xorm:"extends"`
	VoucherTId   int    `xorm:"voucher_t_id" json:"voucher_t_id"`
	VoucherTType int `xorm:"voucher_t_type" json:"voucher_t_type"`
}

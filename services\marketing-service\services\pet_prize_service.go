package services

import (
	po "eShop/domain/marketing-po"
	"eShop/infra/cache"
	"eShop/infra/log"
	"eShop/infra/transaction"
	"eShop/infra/utils"
	"eShop/services/common"
	cachekey "eShop/services/distribution-service/enum/cache-key"
	cache_source "eShop/services/distribution-service/enum/cache-source"
	vo "eShop/view-model/marketing-vo"
	"fmt"
	"time"

	"github.com/spf13/cast"
	"xorm.io/xorm"
)

type PetPrizeService struct {
	common.BaseService
	tm transaction.TransactionManager
}

func NewPetPrizeService() PetPrizeService {
	return PetPrizeService{
		tm: transaction.NewTransactionManager(),
	}
}

// List 获取奖品列表
func (s *PetPrizeService) List(req vo.PetPrizePageReq) (resp []vo.PetPrizeResp, total int64, err error) {
	s.<PERSON>()
	defer s.Close()
	req.EnMobile = utils.MobileEncrypt(req.EnMobile)
	db := s.Engine.Table("pet_prize")

	condition := utils.GetQueryCondition(req)
	if condition != "" {
		db.Where(condition)
	}

	if req.PageIndex > 0 && req.PageSize > 0 {
		db.Limit(req.PageSize, (req.PageIndex-1)*req.PageSize)
	}
	db.OrderBy("create_time desc")
	total, err = db.FindAndCount(&resp)
	if err != nil {
		log.Error("奖品列表查询列表失败", err)
		return
	}

	return
}

func (s *PetPrizeService) GrantRankingPrizes() error {
	s.Begin()
	defer s.Close()

	// 幂等控制：检查是否已发放
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	lockKey := "pet:prize:distributed"
	IsSend := mCache.Get(string(cache_source.EShop), lockKey)
	if cast.ToString(IsSend[0]) == "1" {
		return nil // 已发放
	}

	// redis加锁，防止并发
	lockKey2 := cachekey.PetPrizeLock
	setNxReslt := mCache.TryLock(string(cache_source.EShop), lockKey2, time.Minute*10)
	if !setNxReslt {
		return nil
	}
	defer mCache.Delete(string(cache_source.EShop), lockKey2)

	val := mCache.Get("eShop", "marketing:pet:prizetime") // 活动结束时间
	endTime := val[0]
	if endTime == "" {
		endTime = "2025-09-01 00:00:00"
	}
	today := time.Now().Format("2006-01-02 15:04:05")
	//还没到活动结束的时间
	if cast.ToString(endTime) > today {
		return nil
	}
	// 关联查询所有参赛作品及用户信息
	sql := `
		SELECT
			a.work_code,
			a.vote_count,
			DENSE_RANK() OVER (ORDER BY a.vote_count DESC, a.pk_time ASC) AS vote_count_rank,
			a.create_time,
			c.scrm_user_id,
			c.nick_name,
			c.mobile,
			c.en_mobile
		FROM eshop.pet_artwork a
		LEFT JOIN eshop.pet_contestant c ON a.work_code = c.pk_work_id
		WHERE a.pk_status = 1  AND a.create_time < ?
		ORDER BY vote_count_rank ASC
	`
	type PrizeRow struct {
		WorkCode      string    `xorm:"work_code"`
		VoteCount     int       `xorm:"vote_count"`
		VoteCountRank int       `xorm:"vote_count_rank"`
		CreateTime    time.Time `xorm:"create_time"`
		ScrmUserId    string    `xorm:"scrm_user_id"`
		Nickname      string    `xorm:"nick_name"`
		Mobile        string    `xorm:"mobile"`
		EnMobile      string    `xorm:"en_mobile"`
	}
	var rows []PrizeRow
	if err := s.Engine.SQL(sql, endTime).Find(&rows); err != nil {
		return err
	}

	// 奖项分配
	prizeMap := map[int]string{1: "一等奖", 2: "二等奖", 3: "三等奖"}
	prizeLimit := map[int]int{1: 1, 2: 9, 3: 99}
	//prizeLimit := map[int]int{1: 1, 2: 3, 3: 4}
	countMap := map[int]int{1: 0, 2: 0, 3: 0}

	var prizes []po.PetPrize
	for _, r := range rows {
		var prizeContent string
		switch {
		case r.VoteCountRank == 1 && countMap[1] < prizeLimit[1]:
			prizeContent = prizeMap[1]
			countMap[1]++
		//case r.VoteCountRank <= 10 && r.VoteCountRank > 1 && countMap[2] < prizeLimit[2]:
		case countMap[2] < prizeLimit[2]:
			prizeContent = prizeMap[2]
			countMap[2]++
		//case r.VoteCountRank <= 109 && r.VoteCountRank > 10 && countMap[3] < prizeLimit[3]:
		case countMap[3] < prizeLimit[3]:
			prizeContent = prizeMap[3]
			countMap[3]++
		default:
			continue
		}
		if r.ScrmUserId == "" {
			continue
		}
		prizes = append(prizes, po.PetPrize{
			UserId:        r.ScrmUserId,
			NickName:      r.Nickname,
			Mobile:        r.Mobile,
			EnMobile:      r.EnMobile,
			PrizeType:     1,
			WorkCode:      r.WorkCode,
			PrizeCount:    r.VoteCount,
			ReceiveStatus: 1,
			PrizeContent:  prizeContent,
			CreateTime:    time.Now(),
			UpdateTime:    time.Now(),
		})
	}

	if len(prizes) > 0 {
		_, err := s.Engine.Table(po.PetPrize{}.TableName()).Insert(&prizes)
		if err != nil {
			return err
		}
	}

	// 发放成功，写入redis标记
	mCache.Save(string(cache_source.EShop), lockKey, "1", -1)
	return nil
}

func (s *PetPrizeService) Pop(session *xorm.Session, userId string) (vo.PetPrizePopResp, error) {
	var popResp vo.PetPrizePopResp

	// 拼接redis的key
	cacheKey := fmt.Sprintf(cachekey.PetPrizePop, userId)

	// 获取缓存
	var mCache = cache.NewMemberCache(cache.CacheSources[cache_source.EShop])
	cacheData := mCache.Get(string(cache_source.EShop), cacheKey)
	if cacheData != nil && cacheData[0] != nil {
		voucherId := cacheData[0].(string)
		err := s.tm.NotSupported(session, func(tx *xorm.Session) error {
			_, err := tx.SQL(`SELECT coupon_code,voucher_price,FROM_UNIXTIME(voucher_end_date) AS voucher_end_date,pp.prize_type,pp.prize_count 
			FROM upetmart.upet_voucher uv 
			LEFT JOIN pet_prize pp ON pp.coupon_code=uv.voucher_code 
			WHERE voucher_id=?`, voucherId).Get(&popResp)
			if err != nil {
				log.Error("查询优惠券信息失败", err)
				return err
			}

			return nil
		})
		if err != nil {
			return popResp, err
		}

		// 缓存命中后，删除缓存
		mCache.Delete(string(cache_source.EShop), cacheKey)

		return popResp, nil // 返回缓存中的数据
	}

	return popResp, nil
}

// GetPrizeDetail 获取奖品详情
func (s *PetPrizeService) GetPrizeDetail(userId string) ([]vo.PetPrizeDetailResp, error) {
	s.Begin()
	defer s.Close()

	prizeDefs := []struct {
		PrizeType    int
		PrizeContent string
		PrizeId      int
		VoucherTId   int
		VoucherTType int
	}{
		{6, "5元无门槛券", 0,0,0},
		{5, "15元无门槛券", 0,0,0},
		{4, "30元无门槛券", 0,0,0},
		{3, "三等奖", 0,0,0},
		{2, "二等奖", 0,0,0},
		{1, "一等奖", 0,0,0},
	}

	var result []vo.PetPrizeDetailResp

	for _, def := range prizeDefs {
		var prize po.PetPrizeExt
		has, err := s.Engine.
			Table("pet_prize").Alias("pp").
			Join("LEFT", "upetmart.upet_voucher uv", "uv.voucher_code = pp.coupon_code").
			Join("LEFT", "upetmart.upet_voucher_template ut", "ut.voucher_t_id = uv.voucher_t_id").
			Where("pp.user_id = ? and pp.prize_content = ?", userId, def.PrizeContent).
			Cols("pp.*", "uv.voucher_t_id", "ut.voucher_t_type").
			Get(&prize)
		if err != nil {
			return nil, err
		}

		resp := vo.PetPrizeDetailResp{
			PrizeType:    def.PrizeType,
			PrizeContent: def.PrizeContent,
		}

		if !has {
			resp.ReceiveStatus = 0 // 未解锁
		} else {
			resp.ReceiveStatus = prize.ReceiveStatus
			resp.PrizeId = prize.Id
			resp.VoucherTId = prize.VoucherTId
			resp.VoucherTType = prize.VoucherTType
		}

		result = append(result, resp)
	}

	return result, nil
}

// ReceivePrize 领取奖品
func (s *PetPrizeService) ReceivePrize(req *vo.PetPrizeReceiveReq) error {
	s.Begin()
	defer s.Close()

	// 1. 查询奖品是否存在
	var prize po.PetPrize
	has, err := s.Engine.Where("id =?", req.PrizeId).Get(&prize)
	if err != nil {
		log.Error("查询奖品失败", err)
		return err
	}
	if !has {
		return fmt.Errorf("奖品不存在或不属于当前用户")
	}

	// 2. 检查奖品状态是否为待领取
	if prize.ReceiveStatus != 1 {
		return fmt.Errorf("奖品状态不正确，无法领取")
	}

	// 3. 构建完整地址
	fullAddress := req.DetailAddress

	// 4. 更新奖品信息
	prize.ReceiveStatus = 2 // 已提交地址
	prize.Address = fullAddress
	prize.Receiver = req.Receiver
	// 手机号校验
	if !utils.IsMobile(req.ReceiverMobile) {
		return fmt.Errorf("手机号格式不正确")
	}
	prize.ReceiverMobile = utils.AddStar(req.ReceiverMobile)
	prize.ReceiverEnMobile = utils.MobileEncrypt(req.ReceiverMobile) // 手机号加密
	prize.ReceiveTime = time.Now()
	prize.UpdateTime = time.Now()

	// 5. 保存更新
	_, err = s.Engine.Where("id = ?", prize.Id).Update(&prize)
	if err != nil {
		log.Error("更新奖品信息失败", err)
		return err
	}

	return nil
}

// Package marketing_po 用户作品表领域模型
package marketing_po

import (
	"eShop/infra/errors"
	"time"

	"xorm.io/xorm"
)

const (
	DealStatus_NotDeal = 1 // 未生成
	DealStatus_Success = 2 // 已完成
	DealStatus_Failed  = 3 // 生成失败
)

// PetArtwork 用户作品表
// 对应表：eshop.pet_artwork
// 记录用户参赛作品及其相关信息
// 包含作品编号、用户、宠物信息、AI生成状态、投票、助力、获奖等
type PetArtwork struct {
	// 主键
	Id int `xorm:"pk autoincr 'id'" json:"id"`
	// 作品编号
	WorkCode string `xorm:"work_code" json:"work_code"`
	// 用户ID
	ScrmUserId string `xorm:"scrm_user_id" json:"scrm_user_id"`
	// 宠物昵称
	PetName string `xorm:"pet_name" json:"pet_name"`
	// 宠物性别 0未知 1公 2母
	PetGender int `xorm:"pet_gender" json:"pet_gender"`
	// 宠物品种
	PetBreed string `xorm:"pet_breed" json:"pet_breed"`
	// 宠物照片
	PetPhoto string `xorm:"pet_photo" json:"pet_photo"`
	// 参考图1
	RefImg1 string `xorm:"ref_img1" json:"ref_img1"`
	// 参考图2
	RefImg2 string `xorm:"ref_img2" json:"ref_img2"`
	// 参考图3
	RefImg3 string `xorm:"ref_img3" json:"ref_img3"`
	// 贵族宠物图
	NoblePetImg string `xorm:"noble_pet_img" json:"noble_pet_img"`
	// 血统起源
	BloodOrigin string `xorm:"blood_origin" json:"blood_origin"`
	// 血统占比
	BloodRatio string `xorm:"blood_ratio" json:"blood_ratio"`
	// 基因属性
	GeneAttribute string `xorm:"gene_attribute" json:"gene_attribute"`
	// 贵族气息描述
	NobleDescription string `xorm:"noble_description" json:"noble_description"`
	// 荣获贵族封号
	NobleTitleAward string `xorm:"noble_title_award" json:"noble_title_award"`
	// 贵族气质评分
	NobleScore int `xorm:"noble_score" json:"noble_score"`
	// 气质评分的解释
	NobleScoreDescription string `xorm:"noble_score_description" json:"noble_score_description"`
	// 是否参与PK 0否 1是
	PkStatus int `xorm:"pk_status" json:"pk_status"`
	// AI生成状态：1-未开始 2-已完成 3-失败
	DealStatus int `xorm:"deal_status" json:"deal_status"`
	// AI生成失败原因
	DealFailReason string `xorm:"deal_fail_reason" json:"deal_fail_reason"`
	// 投票数
	VoteCount int `xorm:"vote_count" json:"vote_count"`
	// 助力人数
	AssistCount int `xorm:"assist_count" json:"assist_count"`
	// 参与PK时间
	PkTime time.Time `xorm:"pk_time" json:"pk_time"`
	// 创建时间
	CreateTime time.Time `xorm:"create_time created" json:"create_time"`
	// 更新时间
	UpdateTime time.Time `xorm:"update_time updated" json:"update_time"`
}

// TableName 返回表名
func (PetArtwork) TableName() string {
	return "eshop.pet_artwork"
}

func (p PetArtwork) Create(session *xorm.Session, artwork *PetArtwork) error {
	if session == nil {
		return errors.New("session is nil")
	}
	if artwork == nil {
		return errors.New("artwork is nil")
	}
	_, err := session.Omit("pk_time").Insert(artwork)
	return err
}

// Update 更新作品
func (p PetArtwork) Update(session *xorm.Session, artwork PetArtwork) error {
	_, err := session.ID(artwork.Id).Update(artwork)
	return err
}

// GetByID 根据ID获取作品
func (p PetArtwork) GetByID(session *xorm.Session, id int) (PetArtwork, error) {
	artwork := PetArtwork{}
	has, err := session.ID(id).Get(&artwork)
	if err != nil {
		return artwork, err
	}
	if !has {
		return artwork, errors.NewNotFound("作品不存在")
	}
	return artwork, nil
}

// FindByID 根据ID获取作品
func (p PetArtwork) FindByUserId(session *xorm.Session, userId string) (out []*PetArtwork, hasPk bool, err error) {
	out = make([]*PetArtwork, 0)
	err = session.Where("scrm_user_id = ?", userId).Find(&out)
	if err != nil {
		return
	}
	for _, v := range out {
		if v.PkStatus == 1 {
			hasPk = true
			break
		}
	}
	return
}

package utils

import (
	"crypto/md5"
	"eShop/infra/config"
	"eShop/infra/log"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"sort"
	"strings"
)

const (
	secretKey = "52132e92d85005fd13e2a2bd89b7bed4"
)

// VoucherData 优惠券数据项
type VoucherData struct {
	Tid        int    `json:"tid"`          // 优惠券模板ID
	Phone      string `json:"phone"`        // 手机号（与scrm_user_id二选一）
	ScrmUserId string `json:"scrm_user_id"` // 用户ID（与phone二选一）
	Num        int    `json:"num"`          // 数量
	Type       int    `json:"type"`         // 请求来源
}

// ExchangeVoucherRequest 领取优惠券请求
type ExchangeVoucherRequest struct {
	Data string `json:"data"` // 领取人信息 JSON格式
	Sign string `json:"sign"` // 签名
}

// ExchangeVoucherResponse 领取优惠券响应
// ExchangeVoucherResponse 优惠券兑换响应
type ExchangeVoucherResponse struct {
	Code  int         `json:"code"`  // 状态码
	Datas interface{} `json:"datas"` // 返回数据，成功时为优惠券信息，失败时为错误信息
}

// ExchangeVoucherSuccessData 成功时的数据结构
type ExchangeVoucherSuccessData struct {
	Datas []VoucherExchangeData `json:"datas"` // 返回优惠券信息
}

// ExchangeVoucherErrorData 错误时的数据结构
type ExchangeVoucherErrorData struct {
	Error string `json:"error"` // 错误信息
}

// VoucherExchangeData 优惠券兑换数据
type VoucherExchangeData struct {
	Tid         interface{} `json:"tid"`          // 优惠券模板ID (可能是string或number)
	VoucherId   interface{} `json:"voucherId"`    // 优惠券索引ID (可能是string或number)
	VoucherCode interface{} `json:"voucher_code"` // 优惠券码 (可能是string或number)
}

// GetTidString 获取Tid的字符串值
func (v *VoucherExchangeData) GetTidString() string {
	return fmt.Sprintf("%v", v.Tid)
}

// GetVoucherIdString 获取VoucherId的字符串值
func (v *VoucherExchangeData) GetVoucherIdString() string {
	return fmt.Sprintf("%v", v.VoucherId)
}

// ExchangeVoucher 调用外部接口领取优惠券
// 参数:
//   - voucherDataList: 优惠券领取数据列表
//
// 返回:
//   - 领取到的优惠券数据切片
//   - 错误信息
func ExchangeVoucher(voucherDataList []VoucherData) ([]VoucherExchangeData, error) {
	// 1. 构建data参数
	dataBytes, err := json.Marshal(voucherDataList)
	if err != nil {
		return nil, fmt.Errorf("序列化优惠券数据失败: %v", err)
	}
	data := string(dataBytes)

	// 2. 生成签名
	sign := generateSignature(data)

	// 3. 构建请求参数
	params := url.Values{}
	params.Set("data", data)
	params.Set("sign", sign)

	baseURL := config.Get("mall_api")
	// 4. 构建请求URL
	requestURL := fmt.Sprintf("%s/mobile/index.php?act=openapi&op=exchangeVoucher&%s", baseURL, params.Encode())

	// 5. 发送POST请求
	resp, err := http.PostForm(requestURL, url.Values{
		"data": {data},
		"sign": {sign},
	})
	if err != nil {
		return nil, fmt.Errorf("请求优惠券领取接口失败: %v", err)
	}
	defer resp.Body.Close()

	// 6. 解析响应
	var raw struct {
		Code  int         `json:"code"`
		Datas interface{} `json:"datas"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&raw); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	// 7. 检查响应状态
	if raw.Code != 200 {
		// 处理错误响应
		if errorData, ok := raw.Datas.(map[string]interface{}); ok {
			if errorMsg, exists := errorData["error"]; exists {
				return nil, fmt.Errorf("优惠券领取失败: %v", errorMsg)
			}
		}
		return nil, fmt.Errorf("优惠券领取失败，状态码: %d", raw.Code)
	}

	// 8. 解析成功响应数据
	var voucherData []VoucherExchangeData
	if datasBytes, err := json.Marshal(raw.Datas); err != nil {
		return nil, fmt.Errorf("序列化响应数据失败: %v", err)
	} else if err := json.Unmarshal(datasBytes, &voucherData); err != nil {
		return nil, fmt.Errorf("解析优惠券数据失败: %v", err)
	}

	log.Info("优惠券领取成功:", "requestURL=", requestURL, "data=", data, "sign=", sign, "response=", raw)

	return voucherData, nil
}

// generateSignature 生成签名
func generateSignature(data string) string {
	// 构建签名参数
	param := map[string]string{
		"data": data,
	}

	// 使用电商签名算法
	return GetMallSignature(param)
}

// ExchangeVoucherByPhone 通过手机号领取优惠券
func ExchangeVoucherByPhone(tid int, phone string, num int) ([]VoucherExchangeData, error) {
	voucherData := VoucherData{
		Tid:   tid,
		Phone: phone,
		Num:   num,
		Type:  1,
	}
	return ExchangeVoucher([]VoucherData{voucherData})
}

// ExchangeVoucherByUserId 通过用户ID领取优惠券
func ExchangeVoucherByUserId(tid int, scrmUserId string, num int) ([]VoucherExchangeData, error) {
	voucherData := VoucherData{
		Tid:        tid,
		ScrmUserId: scrmUserId,
		Num:        num,
		Type:       1,
	}
	return ExchangeVoucher([]VoucherData{voucherData})
}

// BatchExchangeVoucher 批量领取优惠券
func BatchExchangeVoucher(voucherDataList []VoucherData) ([]VoucherExchangeData, error) {
	return ExchangeVoucher(voucherDataList)
}

// GetSignature 获取电商签名
// 签名算法：
// 1. 对参数按key进行字典序排序
// 2. 拼接成 key=value& 格式的字符串，过滤空值
// 3. 去掉最后的 & 符号
// 4. 对拼接字符串进行MD5加密
// 5. 在MD5结果后拼接密钥
// 6. 再次进行MD5加密并转大写
//
// 参数:
//   - param: 需要签名的参数字典
//   - secretKey: 签名密钥
//
// 返回:
//   - 生成的签名字符串（大写）
func GetMallSignature(param map[string]string) string {
	// 对参数按键名排序
	keys := make([]string, 0, len(param))
	for key := range param {
		keys = append(keys, key)
	}
	sort.Strings(keys)

	// 拼接参数字符串
	var str strings.Builder
	for _, key := range keys {
		val := param[key]
		if val != "" {
			str.WriteString(key + "=" + val + "&")
		}
	}

	// 去除末尾&
	paramStr := strings.TrimSuffix(str.String(), "&")

	// 第一次MD5 + API密钥 + 第二次MD5并转大写
	firstMd5 := fmt.Sprintf("%x", md5.Sum([]byte(paramStr)))
	finalStr := firstMd5 + secretKey
	return strings.ToUpper(fmt.Sprintf("%x", md5.Sum([]byte(finalStr))))
}

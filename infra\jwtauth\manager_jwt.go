package jwtauth

import (
	"context"
	"crypto/md5"
	"crypto/rsa"
	"eShop/infra/config"
	"eShop/infra/log"
	"eShop/infra/utils"
	viewmodel "eShop/view-model"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/dgrijalva/jwt-go"
	"github.com/spf13/cast"
)

var (
	whiteList           []string
	whiteListPetMedical []string
	PublicKey           *rsa.PublicKey
	PrivateKey          *rsa.PrivateKey
	PetMedicalPublicKey *rsa.PublicKey //互联网医院的公钥
	signKey             string
)

// 医生端来源
const (
	DoctorSourceChannelPC      = 3 //pc
	DoctorSourceChannelAndroid = 1 //安卓
	DoctorSourceChannelIOS     = 2 //iOS

)

var DoctorSourceMap = map[int32]string{
	DoctorSourceChannelPC:      "医生PC端",
	DoctorSourceChannelAndroid: "医生APP端",
	DoctorSourceChannelIOS:     "医生APP端",
}

func JwtInit() {
	PublicKeyString := config.Get("PublicKey")
	PrivateKeyString := config.Get("PrivateKey")

	publicKeyByte, _ := os.ReadFile(PublicKeyString)
	privateKeyByte, _ := os.ReadFile(PrivateKeyString)

	PublicKey, _ = jwt.ParseRSAPublicKeyFromPEM(publicKeyByte)
	PrivateKey, _ = jwt.ParseRSAPrivateKeyFromPEM(privateKeyByte)

	PetMedicalPublicKeyString := "pet_medical_rsa_1024_pub.pem"
	PetMedicalPublicKeyByte, _ := os.ReadFile(PetMedicalPublicKeyString)
	PetMedicalPublicKey, _ = jwt.ParseRSAPublicKeyFromPEM(PetMedicalPublicKeyByte)

	signKey = config.Get("sjan.signKey")

	whiteList = []string{
		// 这里添加白名单路径
		"/api/test/hi",
		"/api/shop/get",
		"/third-brins",
		"/api/rp/dict-data",
		"/manager/stats/shop/order/overview",
		"/manager/stats/sku/city-list",
		"/manager/stats/sku/list",
		"/api/centent/tag-list",
		"/external-app/api/add_store",
		"/inventory-app/io-bound/freeze",
		"/inventory-app/io-bound/unfreeze",
		"/inventory-app/io-bound/order-out",
		"/inventory-app/io-bound/refund-in",
		"/api/blky/sync-code-date",
		"/api/distributor/share",
		"/api/centent/article-list",
		"manager/blky/query-code",

		/*以下接口测完删除*/
		"/inventory-app/voucher/create",
		"/api/distributor/check-boss-invite-code",
		"/api/blky/verify-security-code",

		/*世纪安诺第三方接口*/
		"/external-app/api/sjan/product/add",
		"/external-app/api/sjan/agent/add",
		"/external-app/api/sjan/packaging/add",
		"/external-app/api/sjan/outbound/add",
		"/external-app/api/sjan/return/add",

		//阿里云短信回执
		"/omnibus-app/manager/sms/report",
		"/omnibus-app/manager/sms/send",
		// "/petai-app/api/conversation/chat",
		// "/petai-app/api/conversation/chat/cancel",
		// "/petai-app/api/conversation/create",
		// "/petai-app/api/conversation/detail",
		// "/petai-app/api/conversation/message/list",
		// "/petai-app/api/conversation/list",
		// "/petai-app/api/conversation/file/upload",
		// "/petai-app/api/conversation/file/retrieve",
		"/petai-app/api/pet-medical/conversation/detail",
		"/petai-app/api/pet-medical/conversation/list",
		"/petai-app/api/pet-medical/conversation/message/list",
		"/petai-app/api/pet-medical/conversation/evaluate",
		"/petai-app/api/pet-medical/consult/chat",
		"/petai-app/api/pet-medical/consult/chat/reference-content",
		"/petai-app/api/pet-medical/consult/conversation/message/list",
		"/petai-app/api/data-acquisition/add",
		"/marketing-app/awen/pet-artwork/test",
		"/marketing-app/awen/pet-artwork/chat",
		"/distribution-app/manager/dis/tool/task-do",
		"/manager/dis/tool/task-do",
	}
}

// Verify 拦截操作，用于获取token、校验，并将jwt数据放入请求体上下文中
func Verify(findTokenFns ...func(r *http.Request) string) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		hfn := func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()

			if !sysVerify(r) { //如果非系统级调用，则需要以下逻辑，系统级调用逻辑，参看sysVerify方法体
				// 检查是否在白名单
				var inWhiteList = false
				if len(whiteList) > 0 {
					for _, path := range whiteList {
						if strings.Contains(r.RequestURI, path) {
							inWhiteList = true
							break
						}
					}
				}
				//单独判断swagger
				if strings.Contains(r.RequestURI, "/swagger/") {
					inWhiteList = true
				}

				// 不在白名单中，需要填充claims，以便请求处理中获取用户信息
				if !inWhiteList {
					claims, err := VerifyRequest(r, findTokenFns...)
					if err != nil {
						log.Error("校验并解析token失败, err=" + err.Error())
						resp := viewmodel.BaseHttpResponse{
							Code:    403,
							Message: fmt.Sprintf("valid token required,err=%s，请重新登录", err.Error()),
						}

						bytes, _ := json.Marshal(resp)
						w.Write(bytes)
						return
					} else {
						ctx = NewContext(ctx, claims, err)
					}
				}
			}

			next.ServeHTTP(w, r.WithContext(ctx))
		}
		return http.HandlerFunc(hfn)
	}
}

// PetMedicalVerify 互联网医院账户 拦截操作，用于获取token、校验，并将jwt数据放入请求体上下文中
func PetMedicalVerify() func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		hfn := func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()

			if !sysVerify(r) { //如果非系统级调用，则需要以下逻辑，系统级调用逻辑，参看sysVerify方法体
				// 检查是否在白名单
				var inWhiteList = false
				if len(whiteListPetMedical) > 0 {
					for _, path := range whiteListPetMedical {
						if strings.Contains(r.RequestURI, path) {
							inWhiteList = true
							break
						}
					}
				}
				//单独判断swagger
				if strings.Contains(r.RequestURI, "/swagger/") {
					inWhiteList = true
				}

				// 不在白名单中，需要填充claims，以便请求处理中获取用户信息
				if !inWhiteList {
					claims, err := GetPayloadDirectly(r, true)
					if err != nil {
						log.Error("校验并解析token失败, err=" + err.Error())
						resp := viewmodel.BaseHttpResponse{
							Code:    403,
							Message: fmt.Sprintf("valid token required,err=%s，请重新登录", err.Error()),
						}

						bytes, _ := json.Marshal(resp)
						w.Write(bytes)
						return
					} else {
						ctx = NewContext(ctx, claims, err)
					}
				}
			}

			next.ServeHTTP(w, r.WithContext(ctx))
		}
		return http.HandlerFunc(hfn)
	}
}

// UrlBasedVerify 根据URL路径选择合适的验证方式
func UrlBasedVerify(findTokenFns ...func(r *http.Request) string) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		hfn := func(w http.ResponseWriter, r *http.Request) {
			// 根据URL路径决定使用哪种验证方式
			if strings.Contains(r.URL.Path, "/awen/") {
				// 使用Verify处理其他路径
				Verify(findTokenFns...)(next).ServeHTTP(w, r)
			} else {
				// 使用OfflineVerify处理saas相关的路径
				OfflineVerify(findTokenFns...)(next).ServeHTTP(w, r)
			}
		}
		return http.HandlerFunc(hfn)
	}
}

// 系统内部使用解析token
func GetPayloadDirectly(r *http.Request, skipClaimsValidation bool) (jwt.MapClaims, error) {
	token := ""
	jwtToken := r.Header.Get("Authorization")
	if len(jwtToken) <= 0 {
		return nil, errors.New("无登录信息")
	}
	//source, _ := strconv.Atoi(r.Header.Get("source"))

	index := strings.Index(jwtToken, " ")
	count := strings.Count(jwtToken, "")
	token = jwtToken[index+1 : count-1]

	//來源是app医生端还是pc医生端
	prefix1 := "doctor_app_"
	prefix2 := "doctor_pc_"

	indexSource1 := strings.Index(token, prefix1)
	if indexSource1 == 0 {
		token = token[len(prefix1):]
	}

	indexSource2 := strings.Index(token, prefix2)
	if indexSource2 == 0 {
		token = token[len(prefix2):]
	}

	if len(token) <= 0 {
		return nil, errors.New("无登录信息")
	}

	claims, err := ParseAndGetPayload(token, skipClaimsValidation)
	if err != nil {
		return nil, errors.New(err.Error() + "，token：" + token)
	}

	return claims, nil
}

// 参数tokenStr指的是 从客户端传来的待验证Token
// 验证Token过程中，如果Token生成过程中，指定了iat与exp参数值，将会自动根据时间戳进行时间验证
// 返回payload例子，map[exp:1.562839369e+09 iat:1.562234569e+09 iss:rp-pet.com mobile:18576 nameid:2 role:member]
func ParseAndGetPayload(tokenStr string, skipClaimsValidation bool) (jwt.MapClaims, error) {
	parser := &jwt.Parser{}
	if skipClaimsValidation {
		parser.SkipClaimsValidation = true
	}
	// 基于公钥验证Token合法性
	token, err := parser.Parse(tokenStr, func(token *jwt.Token) (interface{}, error) {
		// 基于JWT的第一部分中的alg字段值进行一次验证
		if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
			return nil, errors.New("验证登录信息的加密类型错误")
		}
		return PetMedicalPublicKey, nil
	},
	)
	if err != nil {
		return nil, err
	}
	claims, ok := token.Claims.(jwt.MapClaims)
	if ok && token.Valid {
		return claims, nil
	}
	return claims, errors.New("登录信息无效或者无对应值")
}

// 宠物saas 线下门店端
func OfflineVerify(findTokenFns ...func(r *http.Request) string) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		hfn := func(w http.ResponseWriter, r *http.Request) {
			// 检查是否在白名单
			var inWhiteList = false
			if len(whiteList) > 0 {
				for _, path := range whiteList {
					if strings.Contains(r.RequestURI, path) {
						inWhiteList = true
						break
					}
				}
			}
			//单独判断swagger
			if strings.Contains(r.RequestURI, "/swagger/") {
				inWhiteList = true
			}
			ctx := r.Context()
			if !inWhiteList {
				claims, err := OfflineVerifyRequest(r, findTokenFns...)
				if err != nil {
					log.Error("校验并解析token失败, err=" + err.Error())
					resp := viewmodel.BaseHttpResponse{
						Code:    403,
						Message: fmt.Sprintf("valid token required,err=%s，请重新登录", err.Error()),
					}

					bytes, _ := json.Marshal(resp)
					w.Write(bytes)
					return
				} else {
					ctx = context.WithValue(ctx, "Claims", claims)
				}
			}
			next.ServeHTTP(w, r.WithContext(ctx))
		}
		return http.HandlerFunc(hfn)
	}
}

type XCShopPayload struct {
	TenantId      string `json:"TenantId"`  //线下门店（线下门店端门店id）
	TenantIds     string `json:"TenantIds"` //线下门店(多个门店)
	Uuid          string `json:"Uuid"`
	UserId        string `json:"UserId"`
	UserName      string `json:"user_name"`
	CustomerId    string `json:"CustomerId"`
	EmployeeId    string `json:"EmployeeId"`
	ChainId       string `json:"ChainId"`
	SourceChainId string `json:"SourceChainId"`
	Iat           int    `json:"iat"`
	Nbf           int    `json:"nbf"`
	Exp           int64  `json:"exp"`
	//0-独立运营,当SourceChainId不为空时，是授权店铺，如果SourceChainId = ChainId,则为:1-被代运营，不相等时为:2-代运营
	RoleType int    `json:"role_type"`
	Token    string `json:"token"`
	jwt.StandardClaims
}

type XCShopJwt struct {
	Payload XCShopPayload `json:"Payload"`
}

func (x *XCShopPayload) getField(name string) string {
	// 根据名称获取到对应成员的值
	return reflect.ValueOf(x).Elem().FieldByName(name).String()
}

func OfflineVerifyRequest(r *http.Request, findTokenFns ...func(r *http.Request) string) (out *XCShopPayload, err error) {
	var tokenString string

	// 从请求头中获取到token
	for _, fn := range findTokenFns {
		tokenString = fn(r)
		if tokenString != "" {
			break
		}
	}
	if tokenString == "" {
		err = errors.New("no token found")
		return
	}
	out = &XCShopPayload{}
	out, err = parseAndValidateToken(tokenString, "pets-cloud_is_a_fantastic_project")
	out.UserName, err = url.QueryUnescape(r.Header.Get("realName"))
	return
	// 分割 Token

	// parts := strings.Split(tokenString, ".")
	// if len(parts) != 3 {
	// 	err = fmt.Errorf("invalid token format")
	// 	return
	// }
	// payload := parts[1]

	// // Base64 解码
	// payloadBytes, err := base64.RawURLEncoding.DecodeString(payload)
	// if err != nil {
	// 	return
	// }

	// // JSON 解析
	// var payloadData XCShopPayload
	// err = json.Unmarshal(payloadBytes, &payloadData)
	// if err != nil {
	// 	return
	// }
	// // 创建 XCShopPayload 结构体
	// out = payloadData

	// out.UserName, err = url.QueryUnescape(r.Header.Get("realName"))
	// if err != nil {
	// 	log.Error("线下门店端用户信息：解析失败：", err.Error())
	// }
	// log.Info("线下门店端用户信息：", utils.InterfaceToJSON(out))

}

// TokenFromHeader 从请求头中获取到token
func TokenFromHeader(r *http.Request) string {
	// Get token from authorization header.
	bearer := r.Header.Get("Authorization")

	if len(bearer) > 7 && strings.ToUpper(bearer[0:7]) == "BEARER " {
		return bearer[7:]
	} else if len(bearer) > 0 {
		return bearer
	}

	return ""
}

// 宠物saas 线下门店端 从头里获取token
func OfflineTokenFromHeader(r *http.Request) string {
	// Get token from authorization header.
	bearer := r.Header.Get("Token")
	if len(bearer) > 7 && strings.ToUpper(bearer[0:7]) == "BEARER " {
		return bearer[7:]
	}
	return bearer
}

// VerifyRequest 校验request中token相关数据
func VerifyRequest(r *http.Request, findTokenFns ...func(r *http.Request) string) (jwt.MapClaims, error) {
	var tokenString string

	// 从请求头中获取到token
	for _, fn := range findTokenFns {
		tokenString = fn(r)
		if tokenString != "" {
			break
		}
	}
	if tokenString == "" {
		return nil, errors.New("no token found")
	}

	//來源是app医生端还是pc医生端
	prefix1 := "doctor_app_"
	prefix2 := "doctor_pc_"

	indexSource1 := strings.Index(tokenString, prefix1)
	if indexSource1 == 0 {
		tokenString = tokenString[len(prefix1):]
	}

	indexSource2 := strings.Index(tokenString, prefix2)
	if indexSource2 == 0 {
		tokenString = tokenString[len(prefix2):]
	}

	return VerifyToken(tokenString, PublicKey)
}

// VerifyToken 校验token的有效性
func VerifyToken(tokenStr string, publicKey *rsa.PublicKey) (jwt.MapClaims, error) {
	// 基于公钥验证Token合法性
	jwtToken, err := jwt.Parse(tokenStr, func(t *jwt.Token) (interface{}, error) {
		if _, ok := t.Method.(*jwt.SigningMethodRSA); !ok {
			return nil, fmt.Errorf("invalid token")
		}
		return publicKey, nil
	})
	if err != nil {
		log.Error(fmt.Sprintf("验证Token失败：%v,tokenStr: %s", tokenStr, err.Error()))
		return nil, err
	}

	claims, ok := jwtToken.Claims.(jwt.MapClaims)
	if ok && jwtToken.Valid {
		//判断是否过期
		nowTime := time.Now().Unix()
		jwtTime := int64(claims["exp"].(float64))
		if nowTime-jwtTime > 0 {
			return nil, errors.New("登录信息已过期")
		}
		return claims, nil
	}
	return nil, err
}

// 宠物saas 解析线下门店端的token
func parseAndValidateToken(tokenString string, secretKey string) (claims *XCShopPayload, err error) {
	claims = &XCShopPayload{}
	// 解析 token
	token, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (interface{}, error) {
		// 验证签名算法
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}

		// 返回密钥
		return []byte(secretKey), nil
	})

	if err != nil {
		return
	}

	if !token.Valid {
		err = fmt.Errorf("token is invalid")
		return
	}

	a := claims.TenantId
	b := claims.Exp
	fmt.Println(a, b)
	// 验证 token 是否过期
	now := time.Now().Unix()
	if claims.Exp < now {
		err = fmt.Errorf("token is expired")
		return
	}

	return claims, nil
}

// NewContext 在请求体内放入基本用户信息
func NewContext(ctx context.Context, c jwt.MapClaims, err error) context.Context {
	ctx = context.WithValue(ctx, "Claims", c)
	ctx = context.WithValue(ctx, "Error", err)
	return ctx
}

// GetPayload 读取jwt内的数据
func GetPayload[T string | jwt.MapClaims](r *http.Request, claimName string) (t T) {
	claims := r.Context().Value("Claims")
	if claims == nil {
		log.Info("当前请求已添加白名单，不需要登录")
		return t
	}

	tokenStr := TokenFromHeader(r)
	if claims != nil && len(tokenStr) == 0 {
		log.Error("没有从请求头中获取到token数据")
		return t
	}

	// claimName有值则取对应名称的数据，没有值则返回claims
	if len(claimName) > 0 {
		t = claims.(jwt.MapClaims)[claimName].(T)
	} else {
		t = claims.(T)
	}
	return t
}

type CtxGetType interface {
	string | int | int64
}

// GetPayload 读取jwt内的数据
func CtxGet[T CtxGetType](ctx context.Context, claimName string) (t T) {
	claims := ctx.Value("Claims")
	if claims == nil {
		log.Info("当前请求已添加白名单，不需要登录")
		return t
	}

	payload, ok := claims.(*XCShopPayload)
	if !ok {
		log.Error("claims类型转换失败")
		return t
	}

	if len(claimName) > 0 {
		value := payload.getField(claimName)
		var zero T
		switch any(zero).(type) {
		case string:
			return any(value).(T)
		case int:
			v, _ := strconv.Atoi(value)
			return any(v).(T)
		case int64:
			v, _ := strconv.ParseInt(value, 10, 64)
			return any(v).(T)
		default:
			log.Error("解析请求头中的", claimName, "失败")
			return t
		}
	}
	return t
}

func CtxSet(ctx context.Context, claims *XCShopPayload) context.Context {
	if ctx == nil {
		ctx = context.Background()
	}

	return context.WithValue(ctx, "Claims", claims)
}

type JwtInfo struct {
	// ==== 前后接口端共有字段 ====
	//手机号
	Mobile string `json:"mobile"`
	//过期时间
	Exp float64 `json:"exp"`

	// ==== 以下为后台接口的jwt token解析字段 ====
	//用户编号
	UserNo string `json:"userNo"`
	//用户名
	Name string `json:"name"`

	// ==== 以下为C端接口的jwt token解析的字段 ====
	Id string `json:"id"` // 小闻养宠助手eshop.user_info.user_info_id字段的值
	//渠道
	Channel string `json:"channel"`
	//rp-pet.com
	Iss string `json:"iss"`
	//用户名
	Openid string `json:"openid"`
	//角色
	Role string `json:"role"`
	//scrmid
	Scrmid string `json:"scrmid"`
	//sessionKey
	SessionKey string `json:"sessionKey"`
	//unionid
	Unionid string `json:"unionid"`
	//Memberid
	Memberid int `json:"member_id"`

	// // ==== 以下为互联网医院医生端 jwt token解析的字段 ====
	UserId     string `json:"userId"`
	IsTest     int    `json:"isTest"`
	UserMobile string `json:"userMobile"`
	UserName   string `json:"userName"`

	// ==== 以下为互联网医院管理端 jwt token解析的字段 ====
	AdminLoginInfo PetMedicalAdminLoginInfo `json:"AdminLoginInfo"`

	// ==== 以下为互联网医院用户端 jwt token解析的字段 ====
	// Scrmid string `json:"scrmid"` //对象编号 - 用户或医生
	// Mobile       string `json:"mobile"`        //对象名称 - 用户或医生
	// Openid       string `json:"openid"`        //对象手机号 - 用户或医生

}

type PetMedicalAdminLoginInfo struct {
	Id        int    `json:"Id"`
	LoginName string `json:"LoginName"`
	Mobile    string `json:"Mobile"`
	Name      string `json:"Name"`
}

// GetJwtInfo 读取jwt内的数据
func GetJwtInfo(r *http.Request) (t JwtInfo, err error) {
	claims := r.Context().Value("Claims")
	if claims == nil {
		log.Info("当前请求已添加白名单，不需要登录")
		return t, errors.New("不需要登录的请求地址，获取不到登录信息")
	}

	tokenStr := TokenFromHeader(r)
	if claims != "" && len(tokenStr) == 0 {
		log.Error("没有从请求头中获取到token数据")
		return t, errors.New("没有从请求数据中找到token信息222")
	}

	// 将 MapClaims 对象序列化为 JSON 字符串
	jsonBytes, err := json.Marshal(claims.(jwt.MapClaims))
	if err != nil {
		log.Error("将 MapClaims 对象序列化为 JSON 字符串异常：e=", err.Error())
		return t, err
	}

	// 将 JSON 字符串反序列化为结构体jwtInfo
	var jwtInfo = JwtInfo{}
	err = json.Unmarshal(jsonBytes, &jwtInfo)
	if err != nil {
		log.Error("将 JSON 字符串反序列化为结构体jwtInfo异常：e=", err.Error())
		return t, err
	}
	return jwtInfo, nil
}

// 宠物Saas 线下门店端 解析token
func GetOfflineJwtInfo(r *http.Request) (t *XCShopPayload, err error) {
	claims := r.Context().Value("Claims")
	if claims == nil {
		log.Info("当前请求已添加白名单，不需要登录")
		return t, errors.New("不需要登录的请求地址，获取不到登录信息")
	}

	tokenStr := OfflineTokenFromHeader(r)
	if claims != "" && len(tokenStr) == 0 {
		log.Error("没有从请求头中获取到token数据")
		return t, errors.New("没有从请求数据中找到token信息")
	}

	// 将 MapClaims 对象序列化为 JSON 字符串
	jsonBytes, err := json.Marshal(claims.(*XCShopPayload))
	if err != nil {
		log.Error("将 MapClaims 对象序列化为 JSON 字符串异常：e=", err.Error())
		return t, err
	}

	// 将 JSON 字符串反序列化为结构体jwtInfo
	t = &XCShopPayload{}
	err = json.Unmarshal(jsonBytes, t)
	if err != nil {
		log.Error("将 JSON 字符串反序列化为结构体jwtInfo异常：e=", err.Error())
		return t, err
	}

	if t.SourceChainId != "0" && t.SourceChainId != "" {
		if t.ChainId == t.SourceChainId {
			t.RoleType = 1
		} else {
			t.RoleType = 2
		}
	}
	return t, nil
}

func sysVerify(r *http.Request) bool {
	var appIdStr = r.Header.Get("appId")
	var timestampStr = r.Header.Get("timestamp")
	var signStr = r.Header.Get("sign")

	fmt.Println(appIdStr, timestampStr, signStr)

	if len(appIdStr) == 0 || len(timestampStr) == 0 || len(signStr) == 0 {
		log.Error("sys-auth授权参数不得为空：appID、timestamp、sign值：", appIdStr, timestampStr, signStr)
		return false
	}

	// 理论上， 需要在系统维护一套 appId 与 securityCode 的映射表，目前写死一套

	securityCode := "69b2f524aa2467ecfa8034e25ea16c5f"
	buffer := fmt.Sprintf("appSecret=%s&appId=%s&timestamp=%s", securityCode, appIdStr, timestampStr)
	hash := md5.New()
	io.WriteString(hash, buffer)
	md5Sum := hash.Sum(nil)
	hexCode := strings.ToUpper(hex.EncodeToString(md5Sum))

	return signStr == hexCode
}

// RequestInfo 请求信息结构体
type RequestInfo struct {
	MemberId int    // 用户ID
	OrgId    int    // 组织ID
	UserId   string // JWT中的用户标识
	OpenId   string // 微信OpenID
	Mobile   string // 手机号
}

// GetRequestInfo 从请求中提取常用信息
func GetRequestInfo(r *http.Request) (*RequestInfo, bool) {
	info := &RequestInfo{
		OrgId: cast.ToInt(r.Header.Get("org_id")),
	}

	// 从jwt里获取用户信息
	jwtInfo, _ := GetJwtInfo(r)
	// if err != nil {
	// 	log.Errorf("%s-鉴权失败-错误为%s", logPrefix, err.Error())
	// 	return nil, false
	// }

	// 填充用户信息
	info.MemberId = jwtInfo.Memberid
	info.UserId = jwtInfo.Scrmid
	info.OpenId = jwtInfo.Openid
	info.Mobile = jwtInfo.Mobile

	return info, true
}

// VerifySign 验证签名
func VerifySign(sign, timestamp, url string) error {
	expectedSign := strings.ToUpper(utils.GetMd5String(fmt.Sprintf("key=%s&timestamp=%s&url=%s", signKey, timestamp, url)))
	if sign != expectedSign {
		log.Errorf("签名错误: sign=%s, expected=%s", sign, expectedSign)
		return errors.New("sign签名错误")
	}
	return nil
}

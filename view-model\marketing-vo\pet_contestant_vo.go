package marketing_vo

// PetContestantListReq 参赛用户列表查询参数
// 用于参赛用户管理列表的多条件查询
type PetContestantListReq struct {
	// 用户手机号
	Mobile string `json:"mobile"`
	// 用户昵称
	NickName string `json:"nick_name"`
	// 是否参与PK 0-全部 2否 1是
	IsPk int `json:"is_pk"`
	// 参与PK的作品id
	PkWorkId string `json:"pk_work_id"`
	// 注册开始时间
	RegisterStart string `json:"register_start"`
	// 注册结束时间
	RegisterEnd string `json:"register_end"`
	// PK开始时间
	PkStart string `json:"pk_start"`
	// PK结束时间
	PkEnd string `json:"pk_end"`
	// 页码
	PageIndex int `json:"page_index"`
	// 每页数量
	PageSize int `json:"page_size"`
	// 是否导出
	IsExport bool `json:"is_export"`
}

// PetContestantListItem 参赛用户列表项
// 用于参赛用户管理列表的单条数据
type PetContestantListItem struct {
	// 主键
	Id int `json:"id"`
	// 用户ID
	ScrmUserId string `json:"scrm_user_id"`
	// 用户昵称
	NickName string `json:"nick_name"`
	// 用户手机号
	Mobile string `json:"mobile"`
	// 手机号加密
	EnMobile string `json:"en_mobile"`
	// 注册时间
	RegisterTime string `json:"register_time"`
	// 客户类型 0老客 1新客
	CustomerType int `json:"customer_type"`
	// 首次创作时间
	FirstWorkTime string `json:"first_work_time"`
	// 已生成作品数量
	WorkCount int `json:"work_count"`
	// 是否参与PK 0否 1是
	IsPk int `json:"is_pk"`
	// 参与PK的作品id
	PkWorkId string `json:"pk_work_id"`
	// 参与PK的时间
	PkTime string `json:"pk_time"`
}

// PetContestantDetail 参赛用户详情
// 用于参赛用户详情展示
type PetContestantDetail struct {
	// 主键
	Id int `json:"id"`
	// 用户ID
	ScrmUserId string `json:"scrm_user_id"`
	// 用户昵称
	Nickname string `json:"nickname"`
	// 用户手机号
	Mobile string `json:"mobile"`
	// 注册时间
	RegisterTime string `json:"register_time"`
	// 客户类型 0新客 1老客
	CustomerType int `json:"customer_type"`
	// 首次创作时间
	FirstWorkTime string `json:"first_work_time"`
	// 已生成作品数量
	WorkCount int `json:"work_count"`
	// 是否参与PK 0否 1是
	IsPk int `json:"is_pk"`
	// 参与PK的作品id
	PkWorkId string `json:"pk_work_id"`
	// 参与PK的时间
	PkTime string `json:"pk_time"`
}

// PetContestantListRes 参赛用户列表响应
// 用于参赛用户管理列表的响应结构
type PetContestantListRes struct {
	// 响应码
	Code int `json:"code"`
	// 响应消息
	Message string `json:"message"`
	// 总数
	Total int `json:"total"`
	// 列表数据
	Data []PetContestantListItem `json:"data"`
}

// PetContestantDetailRes 参赛用户详情响应
// 用于参赛用户详情的响应结构
type PetContestantDetailRes struct {
	// 响应码
	Code int `json:"code"`
	// 响应消息
	Message string `json:"message"`
	// 详情数据
	Data *PetContestantDetail `json:"data"`
}

// MyRankVoteDetail 投票明细项
type MyRankVoteDetail struct {
	// 投票人昵称
	Nickname string `json:"nickname"`
	// 投票人手机号（带*）
	Mobile string `json:"mobile"`
	// 投票时间
	VoteTime string `json:"vote_time"`
	// 投票数
	VoteCount int `json:"vote_count"`
	// 被投票人头像
	InviteeAvatar string `json:"invitee_avatar"`
}

// MyRankReq 我的全国排名请求
type MyRankReq struct {
	UserId string `json:"user_id"`
}

// MyRankRes 我的全国排名响应
type MyRankRes struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    *MyRankData `json:"data"`
}

// MyRankData 我的全国排名数据
type MyRankData struct {
	//作品图片
	PetPhoto string `json:"pet_photo"`
	//作品编号
	WorkCode string `json:"work_code"`
	// 投票数
	VoteCount int `json:"vote_count"`
	// 排名
	Rank int `json:"rank"`
	// 与第一名票数差
	DiffFirst int `json:"diff_first"`
	// 同票数
	SameVoteCount int `json:"same_vote_count"`
	// 与上一名票数差
	DiffPrev int `json:"diff_prev"`
	//同票里面排行多少
	SameVoteSubRank int `json:"same_vote_sub_rank"`
	//同票里面排行多少
	VoteDiffWithNext int `json:"vote_diff_with_next"`
	// 是否存在参赛作品
	IsHave int `json:"is_have"`
}

// MyVoteDetailReq 我的投票明细请求
type MyVoteDetailReq struct {
	//用户ID
	UserId string `json:"user_id"`
	//作品编号
	WorkCode  string `json:"work_code" validate:"required"`
	PageIndex int    `json:"page_index"`
	PageSize  int    `json:"page_size"`
}

// MyVoteDetailRes 我的投票明细响应
type MyVoteDetailRes struct {
	Code    int                `json:"code"`
	Message string             `json:"message"`
	Total   int                `json:"total"`
	Data    []MyRankVoteDetail `json:"data"`
}

// GetQrCodeReq 获取二维码请求
type GetQrCodeReq struct {
	Path    string `json:"path"`     // 路径
	Sid     string `json:"sid"`      // 参数
	OrgId   int    `json:"org_id"`   // 组织ID  1.阿闻 2极宠家
	TypeUrl int    `json:"type_url"` // URL类型 1：base64 2:上传后的URL
}

// GetQrCodeRes 获取二维码响应
type GetQrCodeRes struct {
	Code    int            `json:"code"`
	Message string         `json:"message"`
	Data    *GetQrCodeData `json:"data"`
}

// GetQrCodeData 获取二维码数据
type GetQrCodeData struct {
	URL string `json:"url"` // 二维码URL
}

type PetStatListReq struct {
	PageIndex int    `json:"page_index" form:"page_index"`
	PageSize  int    `json:"page_size" form:"page_size"`
	DateStart string `json:"date_start" form:"date_start"` // yyyy-MM-dd
	DateEnd   string `json:"date_end" form:"date_end"`
	IsExport  bool   `json:"is_export" form:"is_export"` // 是否导出
}

// PetStatListItem 数据监测列表项
type PetStatListItem struct {
	DaliyDate string `json:"daliy_date"`
	Metric1   int    `json:"metric1"`  // 阿闻首页弹窗点击uv
	Metric2   int    `json:"metric2"`  // 阿闻首页banner点击uv
	Metric3   int    `json:"metric3"`  // 阿闻首页浮标点击uv
	Metric4   int    `json:"metric4"`  // 小闻首页广告点击uv
	Metric5   int    `json:"metric5"`  // 贵族首页弹窗点击uv
	Metric6   int    `json:"metric6"`  // 贵族首页banner点击uv
	Metric7   int    `json:"metric7"`  // 贵族活动主页访问的pv
	Metric8   int    `json:"metric8"`  // 贵族活动主页访问uv
	Metric9   int    `json:"metric9"`  // 作品助力页面访问pv
	Metric10  int    `json:"metric10"` // 作品助力页面访问uv
	Metric11  int    `json:"metric11"` // 生成贵族创作图的用户数
	Metric12  int    `json:"metric12"` // 分享的用户数
	Metric13  int    `json:"metric13"` // 贵族宠物图分享总次数
	Metric14  int    `json:"metric14"` // 好友助力总次数
	Metric15  int    `json:"metric15"` // 达到5票的用户数
	Metric16  int    `json:"metric16"` // 达到25票的用户数
}

// PetStatListRes 数据监测列表响应
type PetStatListRes struct {
	Code    int               `json:"code"`
	Message string            `json:"message"`
	Data    []PetStatListItem `json:"data"`
	Total   int               `json:"total"`
}

// SharePetArtworkReq 分享作品请求
type SharePetArtworkReq struct {
	UserId          string `json:"user_id"`          // 邀请人用户ID
	InviterNickname string `json:"inviter_nickname"` // 邀请人昵称
	InviterMobile   string `json:"inviter_mobile"`   // 邀请人手机号
	WorkCode        string `json:"work_code"`        // 作品编号
	InviteeOpenId   string `json:"invitee_open_id"`  // 被邀请人open_id

	InviteeUserId   string `json:"invitee_user_id"`  // 邀请人用户ID
	InviteeNickname string `json:"invitee_nickname"` // 被邀请人昵称
	InviteeMobile   string `json:"invitee_mobile"`   // 被邀请人手机号
}

// SharePetArtworkRes 分享作品响应
type SharePetArtworkRes struct {
	Code    int                  `json:"code"`
	Message string               `json:"message"`
	Data    *SharePetArtworkData `json:"data"`
}

// SharePetArtworkData 分享作品数据
type SharePetArtworkData struct {
	Success bool `json:"success"` // 是否成功
}

// VotePetArtworkReq 投票请求
type VotePetArtworkReq struct {
	UserId          string `json:"user_id"`          // 被邀请人用户ID
	InviterNickname string `json:"inviter_nickname"` // 被邀请人昵称
	InviterMobile   string `json:"inviter_mobile"`   // 被邀请人手机号
	WorkCode        string `json:"work_code"`        // 作品编号
	InviteeOpenId   string `json:"invitee_open_id"`  // 被邀请人open_id
}

// VotePetArtworkRes 投票响应
type VotePetArtworkRes struct {
	Code    int                 `json:"code"`
	Message string              `json:"message"`
	Data    *VotePetArtworkData `json:"data"`
}

// VotePetArtworkData 投票数据
type VotePetArtworkData struct {
	Success     bool `json:"success"`       // 是否成功
	VoteCount   int  `json:"vote_count"`    // 投票数
	IsFirstVote bool `json:"is_first_vote"` // 是否首次投票
}

// PetStatLogReq 埋点请求
type PetStatLogReq struct {
	UniqueId   string `json:"unique_id" `  // 唯一KEY
	UserId     string `json:"user_id"`     // 用户ID
	MetricType int    `json:"metric_type"` // 指标类型
	WorkCode   string `json:"work_code"`   // 作品编号
}

// PetStatLogRes 埋点响应
type PetStatLogRes struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

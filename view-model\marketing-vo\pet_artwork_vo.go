package marketing_vo

// PetArtworkCreateReq 创建作品请求
type PetArtworkCreateReq struct {
	// WorkCode              string `json:"work_code"`               // 作品编号
	ScrmUserId   string `json:"scrm_user_id"`                               // 用户ID（前端不用传）
	ScrmUserName string `json:"scrm_user_name"`                             // 用户名称
	Mobile       string `json:"mobile"`                                     // 手机号（前端不用传）
	PetName      string `json:"pet_name" validate:"required" label:"宠物昵称"`  // 宠物昵称
	PetGender    int    `json:"pet_gender"`                                 // 宠物性别 0未知 1公 2母
	PetPhoto     string `json:"pet_photo" validate:"required" label:"宠物照片"` // 宠物照片
	RefImg1      string `json:"ref_img1"`                                   // 参考图1
	RefImg2      string `json:"ref_img2"`                                   // 参考图2
	RefImg3      string `json:"ref_img3"`                                   // 参考图3

}

type CreateInProgressReq struct {
	ScrmUserId string `json:"scrm_user_id"` // 用户ID（前端不用传）
}

type PetArtworkTestReq struct {
	WorkCode string `json:"work_code"` // 作品编号
}

// PetArtworkUpdateReq 更新作品请求
type PetArtworkUpdateReq struct {
	Id         int    `json:"id"`           // 主键
	ScrmUserId string `json:"scrm_user_id"` // 用户ID
	PkStatus   int    `json:"pk_status"`    // 是否参与PK 0否 1是
	PkTime     string `json:"pk_time"`      // 参与PK时间
}

// PetArtworkPageReq 作品分页查询请求
type PetArtworkPageReq struct {
	WorkCode        string `json:"work_code" query:"pet_artwork.work_code:eq"`            // 作品编号
	ScrmUserId      string `json:"scrm_user_id" query:"pet_artwork.scrm_user_id:eq"`      // 用户ID
	PetName         string `json:"pet_name" query:"pet_name:like"`                        // 宠物昵称
	PkStatus        int    `json:"pk_status" query:"pk_status:eq,allow_zero"`             // 是否参与PK 0否 1是
	DealStatus      int    `json:"deal_status"`                                           // AI生成状态：1-未开始 2-已完成 3-失败
	CreateTimeStart string `json:"create_time_start" query:"pet_artwork.create_time:gte"` // 创建时间开始
	CreateTimeEnd   string `json:"create_time_end" query:"pet_artwork.create_time:lte"`   // 创建时间结束
	PageIndex       int    `json:"page_index"`                                            // 页码
	PageSize        int    `json:"page_size"`                                             // 每页大小
	IsMiniApp       int    `json:"is_mini_app"`                                           // 是否小程序端全国排名
}

// PetArtworkDetailReq 作品详情查询请求
type PetArtworkDetailReq struct {
	Id       int    `json:"id" query:"eq"`        // 主键
	WorkCode string `json:"work_code" query:"eq"` // 作品编号
}

// PetArtworkResp 作品响应结构
type PetArtworkResp struct {
	WorkCode         string `json:"work_code"`          // 作品编号
	ScrmUserId       string `json:"scrm_user_id"`       // 用户ID
	PetName          string `json:"pet_name"`           // 宠物昵称
	PetGender        int    `json:"pet_gender"`         // 宠物性别 0未知 1公 2母
	PetPhoto         string `json:"pet_photo"`          // 宠物照片
	CreateTime       string `json:"create_time"`        // 创建时间
	RefImg1          string `json:"ref_img1"`           // 参考图1
	RefImg2          string `json:"ref_img2"`           // 参考图2
	RefImg3          string `json:"ref_img3"`           // 参考图3
	NoblePetImg      string `json:"noble_pet_img"`      // 贵族宠物图
	NobleScore       int    `json:"noble_score"`        // 贵族气质评分
	BloodRatio       string `json:"blood_ratio"`        // 血统占比
	GeneAttribute    string `json:"gene_attribute"`     // 基因属性
	NobleTitleAward  string `json:"noble_title_award"`  // 荣获贵族封号
	PkStatus         int    `json:"pk_status"`          // 是否参与PK 0否 1是
	VoteCount        int    `json:"vote_count"`         // 投票数
	AssistCount      int    `json:"assist_count"`       // 助力人数
	VoteCountRank    int    `json:"vote_count_rank"`    // 全国排名
	PrizeContent     string `json:"prize_content"`      // 获奖情况
	PetCount         int    `json:"pet_count"`          // 宠物数量
	NobleRankPercent int    `json:"noble_rank_percent"` // 气质评分排名百分比

	PkTime                string `json:"pk_time"`                                  // 参与PK时间
	Id                    int    `json:"id"`                                       // 主键
	PetBreed              string `json:"pet_breed"`                                // 宠物品种
	BloodOrigin           string `json:"blood_origin"`                             // 血统起源
	NobleDescription      string `json:"noble_description"`                        // 贵族气息描述
	NobleScoreDescription string `json:"noble_score_description"`                  // 气质评分的解释
	DealStatus            int    `json:"deal_status"`                              // AI生成状态：1-未开始 2-已完成 3-失败
	DealFailReason        string `xorm:"deal_fail_reason" json:"deal_fail_reason"` // AI生成失败原因
	UpdateTime            string `json:"update_time"`                              // 更新时间
}

type PetArtworkAddRes struct {
	// 响应码
	Code int `json:"code"`
	// 响应消息
	Message string `json:"message"`
	// 总数
	Total int `json:"total"`
	// 列表数据
	Data []PetContestantListItem `json:"data"`
}

type PetArtworkAddReq struct{}

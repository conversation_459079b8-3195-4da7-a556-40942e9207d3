package controllers

import (
	taskEnum "eShop/infra/enum"
	"eShop/infra/jwtauth"
	"eShop/infra/log"
	"eShop/infra/response"
	"eShop/infra/utils"
	"eShop/services/common"
	service "eShop/services/marketing-service/services"
	distribution_vo "eShop/view-model/distribution-vo"
	vo "eShop/view-model/marketing-vo"
	"encoding/json"
	"net/http"

	"github.com/spf13/cast"

	"github.com/go-chi/chi/v5"
)

// PetPrizeController 奖品控制器
type PetPrizeController struct {
	services service.PetPrizeService
}

// NewPetPrizeController 创建奖品控制器
func NewPetPrizeController(services service.PetPrizeService) *PetPrizeController {
	return &PetPrizeController{
		services: services,
	}
}

// RegisterRoutes 注册路由
func (c PetPrizeController) RegisterRoutes(r chi.Router) {
	r.Route("/marketing-app/awen/pet-prize", func(r chi.Router) {
		r.Post("/list", c.List)
		r.Post("/detail", c.Detail)
		r.Post("/receive", c.Receive)
		// 奖励弹窗接口
		r.Get("/pop", c.Pop)
	})
}

// List 获取奖品列表
// @Summary 获取奖品列表
// @Description 获取奖品列表数据
// @Tags 贵族裂变活动-奖品
// @Accept json
// @Produce json
// @Param page body vo.PetPrizePageReq true "列表请求"
// @Success 200 {object} response.Response[[]vo.PetPrizeResp] "成功获取奖品列表数据"
// @Failure 400 {object} response.BaseResp "错误"
// @Router /marketing-app/awen/pet-prize/list [post]
func (c PetPrizeController) List(w http.ResponseWriter, r *http.Request) {
	cmd, err := utils.Bind[vo.PetPrizePageReq](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	if cmd.IsExport == 1 {
		taskServ := common.TaskListService{}
		var task distribution_vo.TaskList
		par, _ := json.Marshal(cmd)
		task.OperationFileUrl = string(par)
		task.OrgId = cast.ToInt(r.Header.Get("org_id"))
		task.TaskContent = taskEnum.TaskContentPetPrizeExport
		err = taskServ.CreatTask(r, task)
		if err != nil {
			log.Errorf("导出领奖列表失败-错误为%s", err.Error())
			response.BadRequest(w, err.Error())
			return
		}
		response.Success(w)
		return
	}

	resp, total, err := c.services.List(cmd) // 传入指针
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithPage(w, resp, int(total))
}

// Pop 奖励弹窗接口
// @Summary 奖励弹窗接口
// @Description 获取奖励弹窗数据
// @Tags 贵族裂变活动-奖品
// @Accept json
// @Produce json
// @Success 200 {object} response.Response[vo.PetPrizeResp] "成功
// @Failure 400 {object} response.BaseResp "错误"
// @Router /marketing-app/awen/pet-prize/pop [get]
func (c *PetPrizeController) Pop(w http.ResponseWriter, r *http.Request) {
	query, err := utils.Bind[vo.PetPrizePopReq](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	resp, err := c.services.Pop(nil, query.UserId)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithData(w, resp)
}

// Detail 获取奖品详情
// @Summary 获取奖品详情
// @Description 获取奖品详情数据
// @Tags 贵族裂变活动-奖品
// @Accept json
// @Produce json
// @Param req body vo.PetPrizeDetailReq true "奖品详情请求"
// @Success 200 {object} response.Response[[]vo.PetPrizeDetailResp] "成功获取奖品详情数据"
// @Failure 400 {object} response.BaseResp "错误"
// @Router /marketing-app/awen/pet-prize/detail [post]
func (c PetPrizeController) Detail(w http.ResponseWriter, r *http.Request) {
	req, err := utils.Bind[vo.PetPrizeDetailReq](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}
	// 从jwt里获取
	if jwtInfo, err := jwtauth.GetJwtInfo(r); err != nil {
		response.BadRequest(w, err.Error())
		return
	} else {
		req.UserId = jwtInfo.Scrmid
	}

	detail, err := c.services.GetPrizeDetail(req.UserId)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.SuccessWithData(w, detail)
}

// Receive 领取奖品
// @Summary 领取奖品
// @Description 提交收货地址领取奖品
// @Tags 贵族裂变活动-奖品
// @Accept json
// @Produce json
// @Param req body vo.PetPrizeReceiveReq true "领取奖品请求"
// @Success 200 {object} response.BaseResp "成功领取奖品"
// @Failure 400 {object} response.BaseResp "错误"
// @Router /marketing-app/awen/pet-prize/receive [post]
func (c PetPrizeController) Receive(w http.ResponseWriter, r *http.Request) {
	req, err := utils.Bind[vo.PetPrizeReceiveReq](r)
	if err != nil {
		response.BadRequest(w, "解析参数错误: "+err.Error())
		return
	}

	// 参数验证
	if req.PrizeId <= 0 || req.UserId == "" || req.Receiver == "" || req.ReceiverMobile == "" || req.DetailAddress == "" {
		response.BadRequest(w, "参数不完整，请填写所有必填项")
		return
	}

	err = c.services.ReceivePrize(&req)
	if err != nil {
		response.BadRequest(w, err.Error())
		return
	}

	response.Success(w)
}
